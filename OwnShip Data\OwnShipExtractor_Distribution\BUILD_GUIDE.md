# Own Ship Extractor - Executable Build Guide

This guide explains how to create a standalone .exe file that can be distributed without requiring Python installation.

## Prerequisites

1. **Python 3.7 or later** installed on your build machine
2. **All application files** in the same directory:
   - `own_ship_extractor.py`
   - `database_manager.py`
   - `data_extractor.py`
   - `cli_interface.py`
   - `MercatorTools.cs`
   - `README.md`
   - `extract_ownship_data.bat`

## Method 1: Automated Build (Recommended)

### Using the Batch File:
1. **Double-click** `build_executable.bat`
2. The script will:
   - Check for Python installation
   - Install required packages (PyInstaller, matplotlib, pandas, tkcalendar)
   - Build the executable
   - Create a distribution package
   - Open the distribution folder

### Using the Python Script:
```bash
python build_exe.py
```

## Method 2: Manual Build

### Step 1: Install PyInstaller
```bash
pip install pyinstaller matplotlib pandas tkcalendar
```

### Step 2: Build the Executable
```bash
pyinstaller --onefile --windowed --name "OwnShipExtractor" --add-data "MercatorTools.cs;." own_ship_extractor.py
```

### Step 3: Test the Executable
```bash
dist\OwnShipExtractor.exe
```

## Build Options Explained

- `--onefile`: Creates a single executable file (easier to distribute)
- `--windowed`: Hides the console window for GUI applications
- `--name "OwnShipExtractor"`: Sets the executable name
- `--add-data "MercatorTools.cs;."`: Includes the MercatorTools.cs file in the executable

## Advanced Build Options

### For Console Application (shows command prompt):
```bash
pyinstaller --onefile --console --name "OwnShipExtractor" own_ship_extractor.py
```

### With Custom Icon:
```bash
pyinstaller --onefile --windowed --icon="icon.ico" --name "OwnShipExtractor" own_ship_extractor.py
```

### For Debugging (keeps temporary files):
```bash
pyinstaller --onefile --windowed --debug=all --name "OwnShipExtractor" own_ship_extractor.py
```

## Distribution Package

After building, you'll get a `OwnShipExtractor_Distribution` folder containing:

```
OwnShipExtractor_Distribution/
├── OwnShipExtractor.exe          # Main executable
├── README.md                     # Complete documentation
├── extract_ownship_data.bat      # Batch file for automation
└── USAGE.txt                     # Quick start guide
```

## File Sizes

- **Single file executable**: ~50-80 MB (includes Python runtime and all dependencies)
- **Directory distribution**: ~30-50 MB (separate files)

## Testing the Executable

### Test GUI Mode:
```bash
OwnShipExtractor.exe
```

### Test CLI Mode:
```bash
OwnShipExtractor.exe --detect
OwnShipExtractor.exe "path\to\database.tzdb" --output "output_folder" --hours 24
```

### Test with Sample Database:
```bash
OwnShipExtractor.exe "OwnShipRecorder.tzdb" --hours 1 --output "test_output"
```

## Troubleshooting

### Common Issues:

1. **"Failed to execute script"**
   - Try building with `--console` to see error messages
   - Check that all Python modules are properly installed

2. **"Module not found"**
   - Add missing modules to the build command:
   ```bash
   pyinstaller --hidden-import=missing_module_name ...
   ```

3. **Large file size**
   - Use `--exclude-module` to remove unused modules:
   ```bash
   pyinstaller --exclude-module=numpy --exclude-module=scipy ...
   ```

4. **Antivirus false positives**
   - This is common with PyInstaller executables
   - Add the executable to antivirus exclusions
   - Consider code signing for distribution

### Debug Build:
If the executable doesn't work, create a debug version:
```bash
pyinstaller --onefile --console --debug=all own_ship_extractor.py
```

## Distribution Notes

### System Requirements for End Users:
- **Windows 7 or later** (64-bit recommended)
- **No Python installation required**
- **~100 MB free disk space**
- **TimeZero software** with OwnShip Recorder data

### Security Considerations:
- The executable is self-contained and portable
- No registry modifications required
- Can be run from any location
- Safe to distribute via email, USB, or network

### Performance:
- **Startup time**: 2-5 seconds (first run may be slower)
- **Memory usage**: ~50-100 MB
- **Performance**: Similar to Python script once loaded

## Alternative Distribution Methods

### 1. Directory Distribution (Smaller):
```bash
pyinstaller --windowed own_ship_extractor.py
```
Creates a `dist/own_ship_extractor/` folder with multiple files.

### 2. Installer Creation:
Use tools like **Inno Setup** or **NSIS** to create a Windows installer.

### 3. Portable App:
Package the executable with **PortableApps.com** format.

## Automation

### For Continuous Integration:
```bash
# Build script for CI/CD
pip install -r requirements.txt
pyinstaller OwnShipExtractor.spec
```

### For Multiple Platforms:
- **Windows**: Use PyInstaller on Windows
- **macOS**: Use PyInstaller on macOS  
- **Linux**: Use PyInstaller on Linux

Each platform requires building on the target OS.

## Final Steps

1. **Test thoroughly** on a clean Windows machine without Python
2. **Document any dependencies** (Visual C++ Redistributables, etc.)
3. **Create installation instructions** for end users
4. **Consider code signing** for professional distribution

The resulting executable will be completely self-contained and ready for distribution! 🚀
