// Profile page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Preview uploaded logo
    const logoInput = document.getElementById('logo');
    if (logoInput) {
        logoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // You could add a preview here if desired
                    console.log('Logo selected:', file.name);
                };
                reader.readAsDataURL(file);
            }
        });
    }
});
