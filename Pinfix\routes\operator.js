const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const QRCode = require('qrcode');
const sharp = require('sharp');
const { body, validationResult } = require('express-validator');
const { loadOperatorContext, requireOperatorAuth, authenticateOperator } = require('../middleware/auth');
const db = require('../models/database');
const { logActivity } = require('../utils/activity-logger');
const emailService = require('../services/email');

const router = express.Router();

// Configure multer for logo uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/logos');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'logo-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|gif|svg/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Configure multer for issue photos
const issuePhotoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/issue-photos');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'issue-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const uploadIssuePhoto = multer({
  storage: issuePhotoStorage,
  limits: { fileSize: 25 * 1024 * 1024 }, // 25MB limit for photos
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only JPEG and PNG image files are allowed'));
    }
  }
});

// Apply operator context loading to all routes with :operatorAccount
router.use('/:operatorAccount', loadOperatorContext);

// Operator login page
router.get('/:operatorAccount/login', async (req, res) => {
  const { operatorAccount } = req.params;

  try {
    const operator = await db.get(
      'SELECT username, name FROM operators WHERE username = ? AND status = "active"',
      [operatorAccount]
    );

    if (!operator) {
      return res.status(404).render('error', {
        title: 'Operator Not Found',
        message: 'The operator account you are looking for does not exist.',
        error: {}
      });
    }

    res.render('auth/operator-login', {
      title: `Login - ${operator.name}`,
      error: null,
      operator: operator
    });
  } catch (error) {
    console.error('Error loading operator login:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'An error occurred while loading the login page.',
      error: {}
    });
  }
});

// Operator login POST
router.post('/:operatorAccount/login', [
  body('username').notEmpty().withMessage('Username is required'),
  body('password').notEmpty().withMessage('Password is required')
], async (req, res) => {
  const { operatorAccount } = req.params;
  const { username, password } = req.body;
  const userType = 'operator'; // Always operator for this route

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const operator = await db.get(
      'SELECT username, name FROM operators WHERE username = ?',
      [operatorAccount]
    );

    return res.render('auth/operator-login', {
      title: `Login - ${operator?.name || operatorAccount}`,
      error: 'Please fill in all fields',
      operator: operator
    });
  }

  try {
    let user = null;

    // Authenticate as operator
    user = await authenticateOperator(username, password, operatorAccount);
    if (user) {
      req.session.operator = {
        id: user.id,
        username: user.username,
        name: user.name
      };
      return res.redirect(`/${operatorAccount}/admin`);
    }

    const operator = await db.get(
      'SELECT username, name FROM operators WHERE username = ?',
      [operatorAccount]
    );

    res.render('auth/operator-login', {
      title: `Login - ${operator?.name || operatorAccount}`,
      error: 'Invalid username or password',
      operator: operator
    });

  } catch (error) {
    console.error('Login error:', error);
    const operator = await db.get(
      'SELECT username, name FROM operators WHERE username = ?',
      [operatorAccount]
    );

    res.render('auth/operator-login', {
      title: `Login - ${operator?.name || operatorAccount}`,
      error: 'An error occurred during login',
      operator: operator
    });
  }
});

// Operator public page (for QR code access)
router.get('/:operatorAccount', (req, res) => {
  res.render('operator/public', {
    title: req.operatorContext.name,
    operator: req.operatorContext,
    session: req.session
  });
});

// Operator admin dashboard (requires authentication)
router.get('/:operatorAccount/admin', requireOperatorAuth, async (req, res) => {
  try {
    // Get operator statistics
    const machineCount = await db.get(
      'SELECT COUNT(*) as count FROM pinball_machines WHERE operator_id = ?',
      [req.operatorContext.id]
    );

    const issueCount = await db.get(`
      SELECT COUNT(*) as count FROM issues
      WHERE operator_id = ?
    `, [req.operatorContext.id]);

    const openIssueCount = await db.get(`
      SELECT COUNT(*) as count FROM issues
      WHERE operator_id = ? AND status = 'Open'
    `, [req.operatorContext.id]);

    const techCount = await db.get(
      'SELECT COUNT(*) as count FROM techs WHERE operator_id = ? AND status = "active"',
      [req.operatorContext.id]
    );

    // Get recent issues for dashboard
    const { formatLocalDate, formatLocalTime } = require('../utils/timezone');
    const timezoneOffset = req.operatorContext.timezone_offset || 0;

    const recentIssues = await db.all(`
      SELECT i.*, gpm.name as machine_name, pm.location_notes
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE i.operator_id = ?
      ORDER BY i.created_at DESC
      LIMIT 5
    `, [req.operatorContext.id]);

    // Add formatted dates to recent issues
    const recentIssuesWithFormattedDates = recentIssues.map(issue => ({
      ...issue,
      formatted_created_date: formatLocalDate(issue.created_at, timezoneOffset),
      formatted_created_time: formatLocalTime(issue.created_at, timezoneOffset)
    }));

    res.render('operator/dashboard', {
      title: 'Operator Dashboard',
      operator: req.operatorContext,
      session: req.session,
      stats: {
        machines: machineCount.count,
        issues: issueCount.count,
        openIssues: openIssueCount.count,
        techs: techCount.count
      },
      recentIssues: recentIssuesWithFormattedDates
    });
  } catch (error) {
    console.error('Operator dashboard error:', error);
    res.render('operator/dashboard', {
      title: 'Operator Dashboard',
      operator: req.operatorContext,
      session: req.session,
      stats: { machines: 0, issues: 0, openIssues: 0, techs: 0 },
      recentIssues: [],
      error: 'Failed to load dashboard statistics'
    });
  }
});

// Operator Profile/Settings
router.get('/:username/admin/profile', requireOperatorAuth, async (req, res) => {
  try {
    const operatorId = req.operatorContext.id;
    const { timezoneOptions } = require('../utils/timezone');

    const operator = await db.get(`
      SELECT id, name, email, phone, address, username, machine_limit, timezone_offset,
             email_notifications_enabled, operator_notifications_enabled, tech_notifications_enabled
      FROM operators
      WHERE id = ?
    `, [operatorId]);

    if (!operator) {
      return res.redirect('/auth/login?error=Operator not found');
    }

    res.render('operator/profile', {
      title: 'Profile Settings',
      operatorContext: req.operatorContext,
      operator: operator,
      timezoneOptions: timezoneOptions
    });
  } catch (error) {
    console.error('Profile page error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/dashboard?error=Failed to load profile`);
  }
});

// Update Operator Profile/Settings
router.post('/:username/admin/profile', requireOperatorAuth, async (req, res) => {
  try {
    const operatorId = req.operatorContext.id;
    const { name, email, phone, address, password, confirm_password, operator_notifications_enabled, tech_notifications_enabled, timezone_offset } = req.body;

    // Validate required fields
    if (!name || !email) {
      return res.redirect(`/${req.operatorContext.username}/admin/profile?error=Name and email are required`);
    }

    // Validate password if provided
    if (password && password.trim()) {
      if (password.length < 6) {
        return res.redirect(`/${req.operatorContext.username}/admin/profile?error=Password must be at least 6 characters long`);
      }
      if (password !== confirm_password) {
        return res.redirect(`/${req.operatorContext.username}/admin/profile?error=Passwords do not match`);
      }
    }

    // Check if email is already used by another operator
    const existingOperator = await db.get(
      'SELECT id FROM operators WHERE email = ? AND id != ?',
      [email, operatorId]
    );

    if (existingOperator) {
      return res.redirect(`/${req.operatorContext.username}/admin/profile?error=Email is already in use by another operator`);
    }

    // Update operator profile (with or without password)
    if (password && password.trim()) {
      console.log('🔑 Updating password for operator:', operatorId);
      const hashedPassword = await bcrypt.hash(password, 10);
      console.log('🔑 Password hashed successfully');

      const result = await db.run(`
        UPDATE operators
        SET name = ?, email = ?, phone = ?, address = ?, password_hash = ?,
            operator_notifications_enabled = ?, tech_notifications_enabled = ?, timezone_offset = ?
        WHERE id = ?
      `, [
        name,
        email,
        phone || null,
        address || null,
        hashedPassword,
        operator_notifications_enabled ? 1 : 0,
        tech_notifications_enabled ? 1 : 0,
        parseInt(timezone_offset) || 0,
        operatorId
      ]);
      console.log('🔑 Password update result:', result);
    } else {
      console.log('📝 Updating profile without password for operator:', operatorId);
      await db.run(`
        UPDATE operators
        SET name = ?, email = ?, phone = ?, address = ?,
            operator_notifications_enabled = ?, tech_notifications_enabled = ?, timezone_offset = ?
        WHERE id = ?
      `, [
        name,
        email,
        phone || null,
        address || null,
        operator_notifications_enabled ? 1 : 0,
        tech_notifications_enabled ? 1 : 0,
        parseInt(timezone_offset) || 0,
        operatorId
      ]);
    }

    // Update the operator context for the current session
    req.operatorContext.name = name;
    req.operatorContext.email = email;
    req.operatorContext.phone = phone;
    req.operatorContext.address = address;

    const successMessage = password && password.trim() ?
      'Profile and password updated successfully' :
      'Profile updated successfully';

    res.redirect(`/${req.operatorContext.username}/admin/profile?success=${encodeURIComponent(successMessage)}`);
  } catch (error) {
    console.error('Profile update error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/profile?error=Failed to update profile`);
  }
});

// Machine Management Routes
router.get('/:operatorAccount/admin/machines', requireOperatorAuth, async (req, res) => {
  try {
    const machines = await db.all(`
      SELECT pm.*, gpm.name, gpm.manufacturer, gpm.machine_type, gpm.date_of_manufacture,
             COUNT(CASE WHEN i.status = 'Open' THEN 1 END) as open_issues_count
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      LEFT JOIN issues i ON pm.id = i.machine_id
      WHERE pm.operator_id = ?
      GROUP BY pm.id
      ORDER BY gpm.name
    `, [req.operatorContext.id]);

    // Get machine limit and current count
    const operatorData = await db.get(`
      SELECT machine_limit
      FROM operators
      WHERE id = ?
    `, [req.operatorContext.id]);

    const activeMachineCount = machines.filter(m => m.status === 'active').length;
    const totalMachineCount = machines.length; // Total machines (active + archived)
    const machineLimit = operatorData?.machine_limit || 10;

    res.render('operator/machines', {
      title: 'Manage Machines',
      operator: req.operatorContext,
      session: req.session,
      machines,
      activeMachineCount,
      totalMachineCount,
      machineLimit,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Machines list error:', error);
    res.render('operator/machines', {
      title: 'Manage Machines',
      operator: req.operatorContext,
      session: req.session,
      machines: [],
      activeMachineCount: 0,
      totalMachineCount: 0,
      machineLimit: 10,
      error: 'Failed to load machines'
    });
  }
});

// Add new machine form
router.get('/:operatorAccount/admin/machines/new', requireOperatorAuth, async (req, res) => {
  try {
    res.render('operator/machine-form', {
      title: 'Add New Machine',
      operator: req.operatorContext,
      session: req.session,
      machine: null,
      isEdit: false
    });
  } catch (error) {
    console.error('Machine form error:', error);
    res.render('operator/machine-form', {
      title: 'Add New Machine',
      operator: req.operatorContext,
      session: req.session,
      machine: null,
      isEdit: false,
      error: 'Failed to load machine options'
    });
  }
});

// AJAX endpoint for machine search
router.get('/:operatorAccount/api/machines/search', requireOperatorAuth, async (req, res) => {
  try {
    const query = req.query.q;

    if (!query || query.length < 2) {
      return res.json([]);
    }

    const searchQuery = `%${query.toLowerCase()}%`;
    const machines = await db.all(`
      SELECT id, name, manufacturer, date_of_manufacture, machine_type
      FROM global_pinball_machines
      WHERE LOWER(name) LIKE ?
         OR LOWER(manufacturer) LIKE ?
         OR LOWER(machine_type) LIKE ?
      ORDER BY
        CASE
          WHEN LOWER(name) LIKE ? THEN 1
          WHEN LOWER(manufacturer) LIKE ? THEN 2
          ELSE 3
        END,
        name
      LIMIT 20
    `, [searchQuery, searchQuery, searchQuery, `${query.toLowerCase()}%`, `${query.toLowerCase()}%`]);

    // Format the response
    const formattedMachines = machines.map(machine => ({
      id: machine.id,
      name: machine.name,
      manufacturer: machine.manufacturer,
      year: machine.date_of_manufacture || 'Unknown',
      type: machine.machine_type
    }));

    res.json(formattedMachines);
  } catch (error) {
    console.error('Machine search error:', error);
    res.status(500).json({ error: 'Search failed' });
  }
});

// Add global machine form (for operators to add missing machines)
router.get('/:operatorAccount/admin/machines/add-global', requireOperatorAuth, (req, res) => {
  res.render('operator/add-global-machine', {
    title: 'Add Machine to Global Database',
    operator: req.operatorContext,
    session: req.session,
    machine: null,
    isEdit: false
  });
});

// Add global machine POST (for operators)
router.post('/:operatorAccount/admin/machines/add-global', requireOperatorAuth, async (req, res) => {
  try {
    const { name, manufacturer, date_of_manufacture, machine_type } = req.body;

    // Validate required fields
    if (!name || !manufacturer || !machine_type) {
      return res.render('operator/add-global-machine', {
        title: 'Add Machine to Global Database',
        operator: req.operatorContext,
        session: req.session,
        machine: req.body,
        isEdit: false,
        error: 'Please fill in all required fields (Name, Manufacturer, and Machine Type)'
      });
    }

    // Check if machine already exists
    const existingMachine = await db.get(
      'SELECT id FROM global_pinball_machines WHERE name = ? AND manufacturer = ?',
      [name, manufacturer]
    );

    if (existingMachine) {
      return res.render('operator/add-global-machine', {
        title: 'Add Machine to Global Database',
        operator: req.operatorContext,
        session: req.session,
        machine: req.body,
        isEdit: false,
        error: 'This machine already exists in the global database'
      });
    }

    // Create machine in global database
    await db.run(`
      INSERT INTO global_pinball_machines (name, manufacturer, date_of_manufacture, machine_type, created_at)
      VALUES (?, ?, ?, ?, datetime('now'))
    `, [name, manufacturer, date_of_manufacture || null, machine_type]);

    // Redirect back to add machine page with success message
    res.redirect(`/${req.operatorContext.username}/admin/machines/new?success=${encodeURIComponent('Machine added to global database! You can now select it from the list.')}`);
  } catch (error) {
    console.error('Create global machine error:', error);
    res.render('operator/add-global-machine', {
      title: 'Add Machine to Global Database',
      operator: req.operatorContext,
      session: req.session,
      machine: req.body,
      isEdit: false,
      error: 'Failed to create machine'
    });
  }
});





// Add new machine
router.post('/:operatorAccount/admin/machines', requireOperatorAuth, async (req, res) => {
  try {
    const { global_machine_id, location_notes } = req.body;
    const operatorId = req.operatorContext.id;

    // Validate required fields
    if (!global_machine_id) {
      const globalMachines = await db.all('SELECT * FROM global_pinball_machines ORDER BY manufacturer, name');
      return res.render('operator/machine-form', {
        title: 'Add New Machine',
        operator: req.operatorContext,
        session: req.session,
        globalMachines,
        machine: req.body,
        isEdit: false,
        error: 'Please select a machine'
      });
    }

    // Check machine limit (count ALL machines, not just active ones)
    const currentMachineCount = await db.get(`
      SELECT COUNT(*) as count
      FROM pinball_machines
      WHERE operator_id = ?
    `, [operatorId]);

    const operatorData = await db.get(`
      SELECT machine_limit
      FROM operators
      WHERE id = ?
    `, [operatorId]);

    const machineLimit = operatorData?.machine_limit || 10;

    if (currentMachineCount.count >= machineLimit) {
      const globalMachines = await db.all('SELECT * FROM global_pinball_machines ORDER BY manufacturer, name');
      return res.render('operator/machine-form', {
        title: 'Add New Machine',
        operator: req.operatorContext,
        session: req.session,
        globalMachines,
        machine: req.body,
        isEdit: false,
        error: `You have reached your machine limit of ${machineLimit} machines. Contact support to increase your limit.`
      });
    }

    // Check if machine already exists for this operator
    const existingMachine = await db.get(
      'SELECT id FROM pinball_machines WHERE operator_id = ? AND global_machine_id = ?',
      [operatorId, global_machine_id]
    );

    if (existingMachine) {
      const globalMachines = await db.all('SELECT * FROM global_pinball_machines ORDER BY manufacturer, name');
      return res.render('operator/machine-form', {
        title: 'Add New Machine',
        operator: req.operatorContext,
        session: req.session,
        globalMachines,
        machine: req.body,
        isEdit: false,
        error: 'You already have this machine in your inventory'
      });
    }

    // Get machine details for activity logging
    const globalMachine = await db.get(
      'SELECT name, manufacturer FROM global_pinball_machines WHERE id = ?',
      [global_machine_id]
    );

    // Create machine
    const result = await db.run(`
      INSERT INTO pinball_machines (operator_id, global_machine_id, location_notes, created_at)
      VALUES (?, ?, ?, datetime('now'))
    `, [operatorId, global_machine_id, location_notes || null]);

    // Generate QR code
    const machineId = result.id;
    const qrCodeUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/${req.operatorContext.username}/machine/${machineId}`;

    // Create QR code directory if it doesn't exist
    const qrDir = path.join(__dirname, '../uploads/qr-codes');
    if (!fs.existsSync(qrDir)) {
      fs.mkdirSync(qrDir, { recursive: true });
    }

    // Generate QR code file
    const qrFileName = `qr-${operatorId}-${machineId}.png`;
    const qrFilePath = path.join(qrDir, qrFileName);

    // First generate QR code to a buffer
    const qrBuffer = await QRCode.toBuffer(qrCodeUrl, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    // Get machine name for the label
    const machineInfo = await db.get(`
      SELECT gpm.name
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE pm.id = ?
    `, [machineId]);

    // Create text image with machine name
    const topTextHeight = 60;
    const bottomTextHeight = 40;
    const qrWidth = 300;
    const totalHeight = topTextHeight + 300 + bottomTextHeight + 30; // top text + QR + bottom text + padding

    // Create a white background with text
    const textSvg = `
      <svg width="${qrWidth}" height="${totalHeight}">
        <rect width="${qrWidth}" height="${totalHeight}" fill="white"/>
        <text x="${qrWidth/2}" y="25" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="black">
          Scan me to
        </text>
        <text x="${qrWidth/2}" y="50" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="black">
          report an issue
        </text>
        <text x="${qrWidth/2}" y="${topTextHeight + 300 + 25}" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="black">
          ${machineInfo ? machineInfo.name : 'Pinball Machine'}
        </text>
      </svg>
    `;

    // Convert SVG to buffer
    const textBuffer = Buffer.from(textSvg);

    // Composite the text and QR code
    await sharp(textBuffer)
      .composite([
        {
          input: qrBuffer,
          top: topTextHeight + 10,
          left: 0
        }
      ])
      .png()
      .toFile(qrFilePath);

    // Update machine with QR code path
    await db.run(
      'UPDATE pinball_machines SET qr_code_path = ? WHERE id = ?',
      [`/uploads/qr-codes/${qrFileName}`, machineId]
    );

    // Log activity
    await logActivity('machine_added', {
      operatorId: operatorId,
      machineId: machineId,
      actorType: 'operator',
      actorName: req.operatorContext.name,
      description: `New machine "${globalMachine.name}" by ${globalMachine.manufacturer} added to location`
    });

    res.redirect(`/${req.operatorContext.username}/admin/machines?success=Machine added successfully with QR code`);
  } catch (error) {
    console.error('Create machine error:', error);
    const globalMachines = await db.all('SELECT * FROM global_pinball_machines ORDER BY manufacturer, name');
    res.render('operator/machine-form', {
      title: 'Add New Machine',
      operator: req.operatorContext,
      session: req.session,
      globalMachines,
      machine: req.body,
      isEdit: false,
      error: 'Failed to create machine'
    });
  }
});

// Edit machine form (must be before /:id routes)
router.get('/:operatorAccount/admin/machines/:id/edit', requireOperatorAuth, async (req, res) => {
  try {
    const machine = await db.get(`
      SELECT pm.*, gpm.name, gpm.manufacturer, gpm.machine_type, gpm.date_of_manufacture
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE pm.id = ? AND pm.operator_id = ?
    `, [req.params.id, req.operatorContext.id]);

    if (!machine) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Machine not found`);
    }

    res.render('operator/machine-edit', {
      title: `Edit ${machine.name}`,
      operator: req.operatorContext,
      session: req.session,
      machine,
      isEdit: true,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Machine edit form error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/machines?error=Failed to load machine edit form`);
  }
});

// Update machine (must be before /:id routes)
router.post('/:operatorAccount/admin/machines/:id/edit', requireOperatorAuth, async (req, res) => {
  try {
    const { location_notes } = req.body;
    const machineId = req.params.id;
    const operatorId = req.operatorContext.id;

    // Verify machine belongs to this operator
    const machine = await db.get(
      'SELECT id FROM pinball_machines WHERE id = ? AND operator_id = ?',
      [machineId, operatorId]
    );

    if (!machine) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Machine not found`);
    }

    // Update machine
    await db.run(
      'UPDATE pinball_machines SET location_notes = ? WHERE id = ?',
      [location_notes || null, machineId]
    );

    res.redirect(`/${req.operatorContext.username}/admin/machines/${machineId}/edit?success=Machine updated successfully`);
  } catch (error) {
    console.error('Update machine error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/machines?error=Failed to update machine`);
  }
});

// Decommission machine (change status to inactive) - must be before /:id routes
router.post('/:operatorAccount/admin/machines/:id/decommission', requireOperatorAuth, async (req, res) => {
  console.log('Decommission route hit:', req.params);
  try {
    const machineId = req.params.id;
    const operatorId = req.operatorContext.id;
    console.log('Machine ID:', machineId, 'Operator ID:', operatorId);

    // Verify machine belongs to this operator
    const machine = await db.get(
      'SELECT id, status FROM pinball_machines WHERE id = ? AND operator_id = ?',
      [machineId, operatorId]
    );
    console.log('Found machine:', machine);

    if (!machine) {
      console.log('Machine not found');
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Machine not found`);
    }

    // Update machine status to archived
    console.log('Updating machine status to archived');
    const result = await db.run(
      'UPDATE pinball_machines SET status = ? WHERE id = ?',
      ['archived', machineId]
    );
    console.log('Update result:', result);

    res.redirect(`/${req.operatorContext.username}/admin/machines/${machineId}?success=Machine decommissioned successfully`);
  } catch (error) {
    console.error('Decommission machine error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/machines?error=Failed to decommission machine`);
  }
});

// Reactivate machine (change status to active) - must be before /:id routes
router.post('/:operatorAccount/admin/machines/:id/reactivate', requireOperatorAuth, async (req, res) => {
  try {
    const machineId = req.params.id;
    const operatorId = req.operatorContext.id;

    // Verify machine belongs to this operator and is currently archived
    const machine = await db.get(
      'SELECT id, status FROM pinball_machines WHERE id = ? AND operator_id = ?',
      [machineId, operatorId]
    );

    if (!machine) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Machine not found`);
    }

    if (machine.status === 'active') {
      return res.redirect(`/${req.operatorContext.username}/admin/machines/${machineId}?error=Machine is already active`);
    }

    // Check machine limit before reactivating
    const currentActiveMachineCount = await db.get(`
      SELECT COUNT(*) as count
      FROM pinball_machines
      WHERE operator_id = ? AND status = 'active'
    `, [operatorId]);

    const operatorData = await db.get(`
      SELECT machine_limit
      FROM operators
      WHERE id = ?
    `, [operatorId]);

    const machineLimit = operatorData?.machine_limit || 10;

    if (currentActiveMachineCount.count >= machineLimit) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines/${machineId}?error=Cannot reactivate machine. You have reached your active machine limit of ${machineLimit} machines. Contact support to increase your limit.`);
    }

    // Update machine status to active
    await db.run(
      'UPDATE pinball_machines SET status = ? WHERE id = ?',
      ['active', machineId]
    );

    res.redirect(`/${req.operatorContext.username}/admin/machines/${machineId}?success=Machine reactivated successfully`);
  } catch (error) {
    console.error('Reactivate machine error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/machines?error=Failed to reactivate machine`);
  }
});

// View machine details and QR code
router.get('/:operatorAccount/admin/machines/:id', requireOperatorAuth, async (req, res) => {
  try {
    const { formatLocalDate, formatLocalTime } = require('../utils/timezone');
    const timezoneOffset = req.operatorContext.timezone_offset || 0;

    const machine = await db.get(`
      SELECT pm.*, gpm.name, gpm.manufacturer, gpm.machine_type, gpm.date_of_manufacture
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE pm.id = ? AND pm.operator_id = ?
    `, [req.params.id, req.operatorContext.id]);

    if (!machine) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Machine not found`);
    }

    // Add formatted date for machine
    machine.formatted_created_date = formatLocalDate(machine.created_at, timezoneOffset);

    // Get recent issues for this machine
    const recentIssues = await db.all(`
      SELECT * FROM issues
      WHERE machine_id = ?
      ORDER BY created_at DESC
      LIMIT 10
    `, [machine.id]);

    // Get count of open issues for this machine
    const openIssuesResult = await db.get(`
      SELECT COUNT(*) as count FROM issues
      WHERE machine_id = ? AND status = 'Open'
    `, [machine.id]);

    const openIssuesCount = openIssuesResult.count;

    // Add formatted dates to issues
    const issuesWithFormattedDates = recentIssues.map(issue => ({
      ...issue,
      formatted_created_date: formatLocalDate(issue.created_at, timezoneOffset),
      formatted_created_time: formatLocalTime(issue.created_at, timezoneOffset)
    }));

    res.render('operator/machine-details', {
      title: `${machine.name} Details`,
      operator: req.operatorContext,
      session: req.session,
      machine,
      recentIssues: issuesWithFormattedDates,
      openIssuesCount
    });
  } catch (error) {
    console.error('Machine details error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/machines?error=Failed to load machine details`);
  }
});





// Issue Management Routes

// List all issues for operator
router.get('/:operatorAccount/admin/issues', requireOperatorAuth, async (req, res) => {
  try {
    const { formatLocalDate, formatLocalTime } = require('../utils/timezone');
    const timezoneOffset = req.operatorContext.timezone_offset || 0;

    const issues = await db.all(`
      SELECT i.*,
             gpm.name as machine_name,
             pm.location_notes,
             t.name as tech_name
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      LEFT JOIN techs t ON i.assigned_tech_id = t.id
      WHERE i.operator_id = ?
      ORDER BY i.created_at DESC
    `, [req.operatorContext.id]);

    // Add formatted dates to each issue
    const issuesWithFormattedDates = issues.map(issue => ({
      ...issue,
      formatted_created_date: formatLocalDate(issue.created_at, timezoneOffset),
      formatted_created_time: formatLocalTime(issue.created_at, timezoneOffset)
    }));

    res.render('operator/issues', {
      title: 'Manage Issues',
      operator: req.operatorContext,
      session: req.session,
      issues: issuesWithFormattedDates,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Issues list error:', error);
    res.render('operator/issues', {
      title: 'Manage Issues',
      operator: req.operatorContext,
      session: req.session,
      issues: [],
      error: 'Failed to load issues'
    });
  }
});

// New issue form
router.get('/:operatorAccount/admin/issues/new', requireOperatorAuth, async (req, res) => {
  try {
    const machineId = req.query.machine_id;

    // Machine ID is required
    if (!machineId) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Please select a machine to report an issue`);
    }

    // Get machine details
    const machine = await db.get(`
      SELECT pm.id, gpm.name, pm.location_notes
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE pm.id = ? AND pm.operator_id = ? AND pm.status = 'active'
    `, [machineId, req.operatorContext.id]);

    if (!machine) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Machine not found or inactive`);
    }

    res.render('operator/issue-form', {
      title: `Report Issue - ${machine.name}`,
      operator: req.operatorContext,
      session: req.session,
      machine,
      issue: null,
      isEdit: false
    });
  } catch (error) {
    console.error('Issue form error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/machines?error=Failed to load issue form`);
  }
});

// Create new issue
router.post('/:operatorAccount/admin/issues', requireOperatorAuth, async (req, res) => {
  try {
    const { machine_id, issue_type, description, priority } = req.body;
    const operatorId = req.operatorContext.id;

    // Validate required fields
    if (!machine_id || !issue_type || !priority) {
      let machine = null;

      // Only try to fetch machine if machine_id is provided
      if (machine_id) {
        try {
          machine = await db.get(`
            SELECT pm.id, gpm.name, pm.location_notes
            FROM pinball_machines pm
            JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
            WHERE pm.id = ? AND pm.operator_id = ?
          `, [machine_id, operatorId]);
        } catch (dbError) {
          console.error('Error fetching machine for validation error:', dbError);
        }
      }

      return res.render('operator/issue-form', {
        title: `Report Issue - ${machine ? machine.name : 'Unknown Machine'}`,
        operator: req.operatorContext,
        session: req.session,
        machine,
        issue: req.body,
        isEdit: false,
        error: 'Please fill in all required fields (Issue Type and Priority are required)'
      });
    }

    // Verify machine belongs to this operator
    const machine = await db.get(
      'SELECT id, global_machine_id FROM pinball_machines WHERE id = ? AND operator_id = ?',
      [machine_id, operatorId]
    );

    if (!machine) {
      return res.redirect(`/${req.operatorContext.username}/admin/machines?error=Invalid machine selected`);
    }

    // Create issue (using user_comment for internal notes)
    await db.run(`
      INSERT INTO issues (
        operator_id, machine_id, global_machine_id, issue_type, priority, user_comment,
        status, created_at
      )
      VALUES (?, ?, ?, ?, ?, ?, 'Open', datetime('now'))
    `, [operatorId, machine_id, machine.global_machine_id, issue_type, priority, description || null]);

    res.redirect(`/${req.operatorContext.username}/admin/machines/${machine_id}?success=Issue reported successfully`);
  } catch (error) {
    console.error('Create issue error:', error);
    let machine = null;

    // Only try to fetch machine if machine_id is provided
    if (req.body.machine_id) {
      try {
        machine = await db.get(`
          SELECT pm.id, gpm.name, pm.location_notes
          FROM pinball_machines pm
          JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
          WHERE pm.id = ? AND pm.operator_id = ?
        `, [req.body.machine_id, req.operatorContext.id]);
      } catch (dbError) {
        console.error('Error fetching machine for catch error:', dbError);
      }
    }

    res.render('operator/issue-form', {
      title: `Report Issue - ${machine ? machine.name : 'Unknown Machine'}`,
      operator: req.operatorContext,
      session: req.session,
      machine,
      issue: req.body,
      isEdit: false,
      error: 'Failed to create issue'
    });
  }
});

// View individual issue
router.get('/:operatorAccount/admin/issues/:id', requireOperatorAuth, async (req, res) => {
  try {
    const { formatLocalDate, formatLocalTime } = require('../utils/timezone');
    const timezoneOffset = req.operatorContext.timezone_offset || 0;

    const issueId = req.params.id;
    const operatorId = req.operatorContext.id;

    // Get issue details with machine and tech information
    const issue = await db.get(`
      SELECT i.*,
             gpm.name as machine_name,
             gpm.manufacturer,
             gpm.machine_type,
             pm.location_notes,
             pm.status as machine_status,
             t.name as tech_name,
             t.email as tech_email,
             comment_tech.name as comment_tech_name,
             comment_operator.name as comment_operator_name
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      LEFT JOIN techs t ON i.assigned_tech_id = t.id
      LEFT JOIN techs comment_tech ON i.resolved_by_tech_id = comment_tech.id
      LEFT JOIN operators comment_operator ON i.resolved_by_operator_id = comment_operator.id
      WHERE i.id = ? AND i.operator_id = ?
    `, [issueId, operatorId]);

    if (!issue) {
      return res.redirect(`/${req.operatorContext.username}/admin/issues?error=Issue not found`);
    }

    // Add formatted dates
    issue.formatted_created_date = formatLocalDate(issue.created_at, timezoneOffset);
    issue.formatted_created_time = formatLocalTime(issue.created_at, timezoneOffset);
    if (issue.resolved_at) {
      issue.formatted_resolved_date = formatLocalDate(issue.resolved_at, timezoneOffset);
      issue.formatted_resolved_time = formatLocalTime(issue.resolved_at, timezoneOffset);
    }
    if (issue.assigned_at) {
      issue.formatted_assigned_date = formatLocalDate(issue.assigned_at, timezoneOffset);
    }

    // Get available techs for assignment
    const availableTechs = await db.all(`
      SELECT id, name, email
      FROM techs
      WHERE operator_id = ? AND status = 'active'
      ORDER BY name
    `, [operatorId]);

    // Get recently not duplicated issues for the same machine
    const notDuplicatedIssues = await db.all(`
      SELECT i.id, i.issue_type, i.created_at, i.assigned_tech_id, i.assigned_at,
             t.name as tech_name
      FROM issues i
      LEFT JOIN techs t ON i.assigned_tech_id = t.id
      WHERE i.machine_id = ?
        AND i.operator_id = ?
        AND i.status = 'Not Duplicated'
        AND i.id != ?
      ORDER BY i.created_at DESC
      LIMIT 10
    `, [issue.machine_id, operatorId, issueId]);

    // Add formatted dates to not duplicated issues
    const notDuplicatedWithDates = notDuplicatedIssues.map(notDupIssue => ({
      ...notDupIssue,
      formatted_created_date: formatLocalDate(notDupIssue.created_at, timezoneOffset)
    }));

    res.render('operator/issue-details', {
      title: `Issue #${issue.id} - ${issue.issue_type}`,
      operator: req.operatorContext,
      session: req.session,
      issue,
      availableTechs,
      notDuplicatedIssues: notDuplicatedWithDates,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Issue details error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/issues?error=Failed to load issue details`);
  }
});

// Mark issue as resolved
router.post('/:operatorAccount/admin/issues/:id/resolve', requireOperatorAuth, async (req, res) => {
  try {
    const issueId = req.params.id;
    const operatorId = req.operatorContext.id;
    const { tech_comment } = req.body;

    // Verify issue belongs to this operator and get issue details
    const issue = await db.get(`
      SELECT i.id, i.issue_type, gpm.name as machine_name
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE i.id = ? AND i.operator_id = ?
    `, [issueId, operatorId]);

    if (!issue) {
      return res.redirect(`/${req.operatorContext.username}/admin/issues?error=Issue not found`);
    }

    // Update issue status to resolved
    await db.run(`
      UPDATE issues
      SET status = 'Resolved',
          resolved_at = datetime('now'),
          updated_at = datetime('now'),
          tech_comment = ?,
          resolved_by_operator_id = ?
      WHERE id = ?
    `, [tech_comment || null, operatorId, issueId]);

    // Log activity
    await logActivity('issue_resolved', {
      operatorId: operatorId,
      issueId: issueId,
      actorType: 'operator',
      actorName: req.operatorContext.name,
      description: `Issue "${issue.issue_type}" resolved for ${issue.machine_name}`
    });

    res.redirect(`/${req.operatorContext.username}/admin/issues/${issueId}?success=Issue marked as resolved`);
  } catch (error) {
    console.error('Resolve issue error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/issues?error=Failed to resolve issue`);
  }
});

// Mark issue as not duplicated
router.post('/:operatorAccount/admin/issues/:id/not-duplicated', requireOperatorAuth, async (req, res) => {
  try {
    const issueId = req.params.id;
    const operatorId = req.operatorContext.id;
    const { tech_comment } = req.body;

    // Verify issue belongs to this operator
    const issue = await db.get(
      'SELECT id FROM issues WHERE id = ? AND operator_id = ?',
      [issueId, operatorId]
    );

    if (!issue) {
      return res.redirect(`/${req.operatorContext.username}/admin/issues?error=Issue not found`);
    }

    // Update issue status to not duplicated
    await db.run(`
      UPDATE issues
      SET status = 'Not Duplicated',
          updated_at = datetime('now'),
          tech_comment = ?,
          resolved_by_operator_id = ?
      WHERE id = ?
    `, [tech_comment || null, operatorId, issueId]);

    res.redirect(`/${req.operatorContext.username}/admin/issues/${issueId}?success=Issue marked as not duplicated`);
  } catch (error) {
    console.error('Mark not duplicated error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/issues?error=Failed to mark issue as not duplicated`);
  }
});

// Assign issue to tech or operator
router.post('/:operatorAccount/admin/issues/:id/assign', requireOperatorAuth, async (req, res) => {
  try {
    const issueId = req.params.id;
    const operatorId = req.operatorContext.id;
    const { assignee_type, tech_id } = req.body;

    // Verify issue belongs to this operator
    const issue = await db.get(
      'SELECT id, status FROM issues WHERE id = ? AND operator_id = ?',
      [issueId, operatorId]
    );

    if (!issue) {
      return res.redirect(`/${req.operatorContext.username}/admin/issues?error=Issue not found`);
    }

    // Can't assign resolved or not duplicated issues
    if (issue.status === 'Resolved' || issue.status === 'Not Duplicated') {
      return res.redirect(`/${req.operatorContext.username}/admin/issues/${issueId}?error=Cannot assign resolved or closed issues`);
    }

    let assignedTechId = null;
    let assignedTo = 'Operator';

    if (assignee_type === 'tech' && tech_id) {
      // Verify tech belongs to this operator
      const tech = await db.get(
        'SELECT id, name FROM techs WHERE id = ? AND operator_id = ? AND status = "active"',
        [tech_id, operatorId]
      );

      if (!tech) {
        return res.redirect(`/${req.operatorContext.username}/admin/issues/${issueId}?error=Selected technician not found or inactive`);
      }

      assignedTechId = tech.id;
      assignedTo = tech.name;
    }

    // Update issue status to assigned
    await db.run(`
      UPDATE issues
      SET status = 'Assigned',
          assigned_tech_id = ?,
          assigned_at = datetime('now'),
          updated_at = datetime('now')
      WHERE id = ?
    `, [assignedTechId, issueId]);

    // Get issue and machine details for activity logging
    const issueDetails = await db.get(`
      SELECT i.issue_type, gpm.name as machine_name
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE i.id = ?
    `, [issueId]);

    // Log activity
    await logActivity('issue_assigned', {
      operatorId: operatorId,
      issueId: issueId,
      techId: assignedTechId,
      actorType: 'operator',
      actorName: req.operatorContext.name,
      description: `Issue "${issueDetails.issue_type}" assigned to ${assignedTo} for ${issueDetails.machine_name}`
    });

    // Send email notification to tech if assigned to a tech (not operator)
    console.log('🔍 Checking tech assignment email:', { assignedTechId, assignee_type, condition: assignedTechId && assignee_type === 'tech' });
    if (assignedTechId && assignee_type === 'tech') {
      try {
        console.log('📧 Preparing tech assignment email...');

        // Get complete data for email
        const operatorData = await db.get(`
          SELECT id, name, email, username, email_notifications_enabled, tech_notifications_enabled
          FROM operators
          WHERE id = ?
        `, [operatorId]);
        console.log('👤 Operator data:', { id: operatorData?.id, email_notifications_enabled: operatorData?.email_notifications_enabled, tech_notifications_enabled: operatorData?.tech_notifications_enabled });

        const techData = await db.get(`
          SELECT id, name, email
          FROM techs
          WHERE id = ?
        `, [assignedTechId]);
        console.log('🔧 Tech data:', { id: techData?.id, name: techData?.name, email: techData?.email });

        const machineData = await db.get(`
          SELECT gpm.name, gpm.manufacturer, pm.location_notes
          FROM pinball_machines pm
          JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
          WHERE pm.id = (SELECT machine_id FROM issues WHERE id = ?)
        `, [issueId]);
        console.log('🎮 Machine data:', { name: machineData?.name, manufacturer: machineData?.manufacturer });

        const issueData = await db.get(`
          SELECT id, issue_type, priority, user_comment, user_picture_path, created_at
          FROM issues
          WHERE id = ?
        `, [issueId]);
        console.log('⚠️ Issue data:', { id: issueData?.id, issue_type: issueData?.issue_type, priority: issueData?.priority });

        // Send tech assignment email
        console.log('📤 Calling sendTechAssignmentNotification...');
        await emailService.sendTechAssignmentNotification(issueData, techData, operatorData, machineData);
      } catch (emailError) {
        console.error('❌ Tech assignment email notification failed:', emailError);
        // Don't break the flow if email fails
      }
    } else {
      console.log('⏭️ Skipping tech email: not assigned to tech or assigned to operator');
    }

    res.redirect(`/${req.operatorContext.username}/admin/issues/${issueId}?success=Issue assigned to ${assignedTo}`);
  } catch (error) {
    console.error('Assign issue error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/issues?error=Failed to assign issue`);
  }
});

// Unassign issue (revert to Open status)
router.post('/:operatorAccount/admin/issues/:id/unassign', requireOperatorAuth, async (req, res) => {
  try {
    const issueId = req.params.id;
    const operatorId = req.operatorContext.id;

    // Verify issue belongs to this operator
    const issue = await db.get(
      'SELECT id, status, assigned_tech_id FROM issues WHERE id = ? AND operator_id = ?',
      [issueId, operatorId]
    );

    if (!issue) {
      return res.redirect(`/${req.operatorContext.username}/admin/issues?error=Issue not found`);
    }

    // Can only unassign if currently assigned
    if (issue.status !== 'Assigned') {
      return res.redirect(`/${req.operatorContext.username}/admin/issues/${issueId}?error=Issue is not currently assigned`);
    }

    // Update issue status back to Open and clear assignment
    await db.run(`
      UPDATE issues
      SET status = 'Open',
          assigned_tech_id = NULL,
          assigned_at = NULL,
          updated_at = datetime('now')
      WHERE id = ?
    `, [issueId]);

    res.redirect(`/${req.operatorContext.username}/admin/issues/${issueId}?success=Issue unassigned and reverted to Open status`);
  } catch (error) {
    console.error('Unassign issue error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/issues?error=Failed to unassign issue`);
  }
});

// Public QR code issue reporting (no auth required)
router.get('/:operatorAccount/machine/:id', async (req, res) => {
  try {
    const machineId = req.params.id;
    const operatorAccount = req.params.operatorAccount;

    // Get machine and operator details
    const machine = await db.get(`
      SELECT pm.id, gpm.name, pm.location_notes, pm.status,
             o.name as operator_name, o.username as operator_username
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      JOIN operators o ON pm.operator_id = o.id
      WHERE pm.id = ? AND o.username = ? AND pm.status = 'active'
    `, [machineId, operatorAccount]);

    if (!machine) {
      return res.status(404).render('error', {
        title: 'Machine Not Found',
        message: 'This machine is not available for issue reporting.',
        error: { status: 404 }
      });
    }

    res.render('public/report-issue', {
      title: `Report Issue - ${machine.name}`,
      machine,
      operator: { name: machine.operator_name, username: machine.operator_username },
      issue: null
    });
  } catch (error) {
    console.error('Public issue form error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load issue reporting form.',
      error: { status: 500 }
    });
  }
});

// Helper function to handle upload errors
async function handleUploadError(req, res, errorMessage) {
  try {
    const machineId = req.params.id;
    const operatorAccount = req.params.operatorAccount;

    const machine = await db.get(`
      SELECT pm.id, gpm.name, pm.location_notes,
             o.name as operator_name, o.username as operator_username
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      JOIN operators o ON pm.operator_id = o.id
      WHERE pm.id = ? AND o.username = ?
    `, [machineId, operatorAccount]);

    if (!machine) {
      return res.status(404).render('error', {
        title: 'Machine Not Found',
        message: 'This machine is not available for issue reporting.',
        error: { status: 404 }
      });
    }

    return res.render('public/report-issue', {
      title: `Report Issue - ${machine.name}`,
      machine,
      operator: { name: machine.operator_name, username: machine.operator_username },
      issue: req.body,
      error: errorMessage
    });
  } catch (error) {
    console.error('Error handling upload error:', error);
    return res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load issue reporting form.',
      error: { status: 500 }
    });
  }
}

// Submit public issue report
router.post('/:operatorAccount/machine/:id/report', (req, res, next) => {
  uploadIssuePhoto.single('photo')(req, res, (err) => {
    if (err) {
      console.error('Upload error:', err);

      // Handle specific multer errors
      if (err.code === 'LIMIT_FILE_SIZE') {
        return handleUploadError(req, res, 'Photo is too large. Please choose a smaller image (max 25MB).');
      } else if (err.message === 'Only JPEG and PNG image files are allowed') {
        return handleUploadError(req, res, 'Only JPEG and PNG image files are allowed.');
      } else {
        return handleUploadError(req, res, 'Failed to upload photo. Please try again.');
      }
    }
    next();
  });
}, async (req, res) => {
  try {
    const machineId = req.params.id;
    const operatorAccount = req.params.operatorAccount;
    const { issue_type, description, priority } = req.body;

    // Validate required fields
    if (!issue_type || !priority) {
      const machine = await db.get(`
        SELECT pm.id, gpm.name, pm.location_notes,
               o.name as operator_name, o.username as operator_username
        FROM pinball_machines pm
        JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
        JOIN operators o ON pm.operator_id = o.id
        WHERE pm.id = ? AND o.username = ?
      `, [machineId, operatorAccount]);

      return res.render('public/report-issue', {
        title: `Report Issue - ${machine.name}`,
        machine,
        operator: { name: machine.operator_name, username: machine.operator_username },
        issue: req.body,
        error: 'Please fill in all required fields (Issue Type and Priority)'
      });
    }

    // Get machine and operator details
    const machine = await db.get(`
      SELECT pm.id, pm.operator_id, pm.global_machine_id,
             o.username as operator_username
      FROM pinball_machines pm
      JOIN operators o ON pm.operator_id = o.id
      WHERE pm.id = ? AND o.username = ? AND pm.status = 'active'
    `, [machineId, operatorAccount]);

    if (!machine) {
      return res.status(404).render('error', {
        title: 'Machine Not Found',
        message: 'This machine is not available for issue reporting.',
        error: { status: 404 }
      });
    }

    // Handle photo upload
    let photoPath = null;
    if (req.file) {
      photoPath = `/uploads/issue-photos/${req.file.filename}`;
    }

    // Create issue (reported by external user via QR code)
    const result = await db.run(`
      INSERT INTO issues (
        operator_id, machine_id, global_machine_id, issue_type, priority, user_comment, user_picture_path,
        status, created_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, 'Open', datetime('now'))
    `, [machine.operator_id, machineId, machine.global_machine_id, issue_type, priority, description || null, photoPath]);

    // Get machine and operator info for activity logging
    const machineInfo = await db.get(`
      SELECT gpm.name as machine_name, o.name as operator_name
      FROM pinball_machines pm
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      JOIN operators o ON pm.operator_id = o.id
      WHERE pm.id = ?
    `, [machineId]);

    // Log activity
    await logActivity('issue_created', {
      operatorId: machine.operator_id,
      machineId: machineId,
      issueId: result.id,
      actorType: 'system',
      actorName: 'Customer',
      description: `New ${priority.toLowerCase()} priority issue "${issue_type}" reported for ${machineInfo.machine_name} at ${machineInfo.operator_name}`
    });

    // Send email notification to operator
    try {
      // Get complete operator and machine data for email
      const operatorData = await db.get(`
        SELECT id, name, email, username, email_notifications_enabled, operator_notifications_enabled
        FROM operators
        WHERE id = ?
      `, [machine.operator_id]);

      const machineData = await db.get(`
        SELECT gpm.name, gpm.manufacturer, pm.location_notes
        FROM pinball_machines pm
        JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
        WHERE pm.id = ?
      `, [machineId]);

      const issueData = {
        id: result.id,
        issue_type: issue_type,
        priority: priority,
        user_comment: description,
        user_picture_path: photoPath,
        created_at: new Date().toISOString()
      };

      // Send email notification
      await emailService.sendIssueNotification(issueData, operatorData, machineData);
    } catch (emailError) {
      console.error('Email notification failed:', emailError);
      // Don't break the flow if email fails
    }

    res.render('public/report-success', {
      title: 'Issue Reported Successfully',
      operator: { username: machine.operator_username }
    });
  } catch (error) {
    console.error('Public issue submission error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to submit issue report.',
      error: { status: 500 }
    });
  }
});


// Tech Management Routes

// List all techs for operator
router.get('/:operatorAccount/admin/techs', requireOperatorAuth, async (req, res) => {
  try {
    const techs = await db.all(`
      SELECT * FROM techs
      WHERE operator_id = ?
      ORDER BY name
    `, [req.operatorContext.id]);

    res.render('operator/techs', {
      title: 'Manage Techs',
      operator: req.operatorContext,
      session: req.session,
      techs,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Techs list error:', error);
    res.render('operator/techs', {
      title: 'Manage Techs',
      operator: req.operatorContext,
      session: req.session,
      techs: [],
      error: 'Failed to load techs list'
    });
  }
});

// Add new tech form
router.get('/:operatorAccount/admin/techs/new', requireOperatorAuth, (req, res) => {
  res.render('operator/tech-form', {
    title: 'Add New Tech',
    operator: req.operatorContext,
    session: req.session,
    tech: null,
    isEdit: false,
    error: null
  });
});

// Create new tech
router.post('/:operatorAccount/admin/techs', requireOperatorAuth, async (req, res) => {
  try {
    const { name, email, username, password } = req.body;
    const operatorId = req.operatorContext.id;

    // Validate required fields
    if (!name || !email || !username || !password) {
      return res.render('operator/tech-form', {
        title: 'Add New Tech',
        operator: req.operatorContext,
        session: req.session,
        tech: req.body,
        isEdit: false,
        error: 'Please fill in all required fields'
      });
    }

    // Check if username already exists for this operator
    const existingTech = await db.get(
      'SELECT id FROM techs WHERE operator_id = ? AND username = ?',
      [operatorId, username]
    );

    if (existingTech) {
      return res.render('operator/tech-form', {
        title: 'Add New Tech',
        operator: req.operatorContext,
        session: req.session,
        tech: req.body,
        isEdit: false,
        error: 'Username already exists for this operator'
      });
    }

    // Hash password
    const bcrypt = require('bcryptjs');
    const passwordHash = await bcrypt.hash(password, 10);

    // Create tech
    const result = await db.run(`
      INSERT INTO techs (operator_id, name, email, username, password_hash)
      VALUES (?, ?, ?, ?, ?)
    `, [operatorId, name, email, username, passwordHash]);

    // Log activity
    await logActivity('tech_added', {
      operatorId: operatorId,
      techId: result.id,
      actorType: 'operator',
      actorName: req.operatorContext.name,
      description: `New technician "${name}" added to team`
    });

    res.redirect(`/${req.operatorContext.username}/admin/techs?success=Tech added successfully`);
  } catch (error) {
    console.error('Create tech error:', error);
    res.render('operator/tech-form', {
      title: 'Add New Tech',
      operator: req.operatorContext,
      session: req.session,
      tech: req.body,
      isEdit: false,
      error: 'Failed to create tech'
    });
  }
});

// Edit tech form
router.get('/:operatorAccount/admin/techs/:id/edit', requireOperatorAuth, async (req, res) => {
  try {
    const tech = await db.get(`
      SELECT * FROM techs
      WHERE id = ? AND operator_id = ?
    `, [req.params.id, req.operatorContext.id]);

    if (!tech) {
      return res.redirect(`/${req.operatorContext.username}/admin/techs?error=Tech not found`);
    }

    res.render('operator/tech-form', {
      title: `Edit ${tech.name}`,
      operator: req.operatorContext,
      session: req.session,
      tech,
      isEdit: true,
      error: null
    });
  } catch (error) {
    console.error('Tech edit form error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/techs?error=Failed to load tech edit form`);
  }
});

// Update tech
router.post('/:operatorAccount/admin/techs/:id/edit', requireOperatorAuth, async (req, res) => {
  try {
    const { name, email, username, password } = req.body;
    const techId = req.params.id;
    const operatorId = req.operatorContext.id;

    // Verify tech belongs to this operator
    const tech = await db.get(
      'SELECT id FROM techs WHERE id = ? AND operator_id = ?',
      [techId, operatorId]
    );

    if (!tech) {
      return res.redirect(`/${req.operatorContext.username}/admin/techs?error=Tech not found`);
    }

    // Validate required fields
    if (!name || !email || !username) {
      const techData = await db.get('SELECT * FROM techs WHERE id = ?', [techId]);
      return res.render('operator/tech-form', {
        title: `Edit ${techData.name}`,
        operator: req.operatorContext,
        session: req.session,
        tech: { ...techData, ...req.body },
        isEdit: true,
        error: 'Please fill in all required fields'
      });
    }

    // Check if username already exists for another tech
    const existingTech = await db.get(
      'SELECT id FROM techs WHERE operator_id = ? AND username = ? AND id != ?',
      [operatorId, username, techId]
    );

    if (existingTech) {
      const techData = await db.get('SELECT * FROM techs WHERE id = ?', [techId]);
      return res.render('operator/tech-form', {
        title: `Edit ${techData.name}`,
        operator: req.operatorContext,
        session: req.session,
        tech: { ...techData, ...req.body },
        isEdit: true,
        error: 'Username already exists for another tech'
      });
    }

    // Update tech (with or without password)
    if (password && password.trim()) {
      const bcrypt = require('bcryptjs');
      const passwordHash = await bcrypt.hash(password, 10);
      await db.run(`
        UPDATE techs
        SET name = ?, email = ?, username = ?, password_hash = ?, updated_at = datetime('now')
        WHERE id = ?
      `, [name, email, username, passwordHash, techId]);
    } else {
      await db.run(`
        UPDATE techs
        SET name = ?, email = ?, username = ?, updated_at = datetime('now')
        WHERE id = ?
      `, [name, email, username, techId]);
    }

    res.redirect(`/${req.operatorContext.username}/admin/techs?success=Tech updated successfully`);
  } catch (error) {
    console.error('Update tech error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/techs?error=Failed to update tech`);
  }
});

// Deactivate tech
router.post('/:operatorAccount/admin/techs/:id/deactivate', requireOperatorAuth, async (req, res) => {
  try {
    const techId = req.params.id;
    const operatorId = req.operatorContext.id;

    // Verify tech belongs to this operator
    const tech = await db.get(
      'SELECT id, name FROM techs WHERE id = ? AND operator_id = ?',
      [techId, operatorId]
    );

    if (!tech) {
      return res.redirect(`/${req.operatorContext.username}/admin/techs?error=Tech not found`);
    }

    // Check if tech has any assigned issues
    const assignedIssues = await db.all(
      'SELECT id FROM issues WHERE assigned_tech_id = ? AND status = "Assigned"',
      [techId]
    );

    // Begin transaction to ensure data consistency
    await db.run('BEGIN TRANSACTION');

    try {
      // Update tech status to inactive
      await db.run(
        'UPDATE techs SET status = ?, updated_at = datetime(\'now\') WHERE id = ?',
        ['inactive', techId]
      );

      // Revert assigned issues back to Open status
      if (assignedIssues.length > 0) {
        await db.run(`
          UPDATE issues
          SET status = 'Open',
              assigned_tech_id = NULL,
              assigned_at = NULL,
              updated_at = datetime('now')
          WHERE assigned_tech_id = ? AND status = 'Assigned'
        `, [techId]);
      }

      // Commit transaction
      await db.run('COMMIT');

      const message = assignedIssues.length > 0
        ? `Tech deactivated successfully. ${assignedIssues.length} assigned issue(s) reverted to Open status.`
        : 'Tech deactivated successfully';

      res.redirect(`/${req.operatorContext.username}/admin/techs?success=${encodeURIComponent(message)}`);
    } catch (transactionError) {
      // Rollback transaction on error
      await db.run('ROLLBACK');
      throw transactionError;
    }
  } catch (error) {
    console.error('Deactivate tech error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/techs?error=Failed to deactivate tech`);
  }
});

// Reactivate tech
router.post('/:operatorAccount/admin/techs/:id/reactivate', requireOperatorAuth, async (req, res) => {
  try {
    const techId = req.params.id;
    const operatorId = req.operatorContext.id;

    // Verify tech belongs to this operator
    const tech = await db.get(
      'SELECT id FROM techs WHERE id = ? AND operator_id = ?',
      [techId, operatorId]
    );

    if (!tech) {
      return res.redirect(`/${req.operatorContext.username}/admin/techs?error=Tech not found`);
    }

    // Update tech status to active
    await db.run(
      'UPDATE techs SET status = ?, updated_at = datetime(\'now\') WHERE id = ?',
      ['active', techId]
    );

    res.redirect(`/${req.operatorContext.username}/admin/techs?success=Tech reactivated successfully`);
  } catch (error) {
    console.error('Reactivate tech error:', error);
    res.redirect(`/${req.operatorContext.username}/admin/techs?error=Failed to reactivate tech`);
  }
});

module.exports = router;
