// Chart colors
const colors = {
    primary: '#0d6efd',
    secondary: '#6c757d',
    success: '#198754',
    danger: '#dc3545',
    warning: '#ffc107',
    info: '#0dcaf0',
    light: '#f8f9fa',
    dark: '#212529'
};

const chartColors = [
    colors.primary, colors.success, colors.warning, colors.danger, 
    colors.info, colors.secondary, '#e83e8c', '#fd7e14', '#20c997', '#6f42c1'
];

// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get data from global variables set by the template
    const chartData = window.chartData || {};

    // Machine Type Chart
    if (chartData.issuesByMachineType && chartData.issuesByMachineType.length > 0) {
        const machineTypeData = chartData.issuesByMachineType;
        new Chart(document.getElementById('machineTypeChart'), {
            type: 'doughnut',
            data: {
                labels: machineTypeData.map(item => item.machine_type || 'Unknown'),
                datasets: [{
                    data: machineTypeData.map(item => item.issue_count),
                    backgroundColor: chartColors.slice(0, machineTypeData.length)
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Status Chart
    if (chartData.issueStatusDistribution && chartData.issueStatusDistribution.length > 0) {
        const statusData = chartData.issueStatusDistribution;
        const statusColors = {
            'Open': colors.danger,
            'Assigned': colors.info,
            'Resolved': colors.success,
            'Not Duplicated': colors.secondary
        };
        
        new Chart(document.getElementById('statusChart'), {
            type: 'doughnut',
            data: {
                labels: statusData.map(item => item.status),
                datasets: [{
                    data: statusData.map(item => item.count),
                    backgroundColor: statusData.map(item => statusColors[item.status] || colors.secondary)
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Issues Over Time Chart
    if (chartData.issuesOverTime && chartData.issuesOverTime.length > 0) {
        const issuesOverTimeData = chartData.issuesOverTime;
        new Chart(document.getElementById('issuesOverTimeChart'), {
            type: 'line',
            data: {
                labels: issuesOverTimeData.map(item => {
                    const date = new Date(item.date);
                    return date.toLocaleDateString();
                }),
                datasets: [{
                    label: 'Issues Created',
                    data: issuesOverTimeData.map(item => item.count),
                    borderColor: colors.primary,
                    backgroundColor: colors.primary + '20',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // Weekly Chart
    if (chartData.weeklyIssues && chartData.weeklyIssues.length > 0) {
        const weeklyData = chartData.weeklyIssues;
        new Chart(document.getElementById('weeklyChart'), {
            type: 'bar',
            data: {
                labels: weeklyData.map(item => item.week),
                datasets: [{
                    label: 'Issues',
                    data: weeklyData.map(item => item.count),
                    backgroundColor: colors.success
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // Monthly Chart
    if (chartData.monthlyIssues && chartData.monthlyIssues.length > 0) {
        const monthlyData = chartData.monthlyIssues;
        new Chart(document.getElementById('monthlyChart'), {
            type: 'bar',
            data: {
                labels: monthlyData.map(item => {
                    const [year, month] = item.month.split('-');
                    const date = new Date(year, month - 1);
                    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                }),
                datasets: [{
                    label: 'Issues',
                    data: monthlyData.map(item => item.count),
                    backgroundColor: colors.warning
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
});
