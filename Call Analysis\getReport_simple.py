#!/usr/bin/env python3
"""
RingCX API Report Query Module (Simplified)
This module provides a framework for querying RingCX API call reports.
Currently experiencing 403 Forbidden errors due to insufficient permissions.
"""

import requests
import json
from datetime import datetime, timedelta
from rc_auth import get_ringcx_access_token_from_rc_credentials, extract_access_token

def get_ringcx_access_token():
    """Get RingCX access token using rc_auth.py authentication."""
    try:
        response = get_ringcx_access_token_from_rc_credentials()
        return extract_access_token(response)
    except Exception as e:
        raise RuntimeError(f"Failed to get RingCX access token: {str(e)}")

def create_call_report_payload(start_date=None, end_date=None):
    """
    Create the exact payload from your specification.
    
    Args:
        start_date (str, optional): Start date in format "2025-06-01T00:00:00.000-0000"
        end_date (str, optional): End date in format "2025-06-01T00:00:00.000-0000"
    
    Returns:
        dict: Request payload for RingCX API
    """
    # Use your exact example date if not provided
    if not start_date:
        start_date = "2025-06-01T00:00:00.000-0000"
    
    # Create the exact payload from your specification
    payload = {
        "reportType": "GLOBAL_CALL_TYPE_DELIMITED",
        "reportCriteria": {
            "criteriaType": "GLOBAL_CALL_TYPE_CRITERIA",
            "startDate": start_date,
            "containGates": True,
            "containCampaigns": True,
            "containIvrStudios": True,
            "containCloudProfiles": True,
            "containTracNumbers": True,
            "containAgents": True,
            "includeNoAnswers": False
        }
    }
    
    # Add end date if provided
    if end_date:
        payload["reportCriteria"]["endDate"] = end_date
    
    return payload

def query_ringcx_reports(start_date=None, end_date=None):
    """
    Query RingCX API for call reports using your exact specification.
    
    Args:
        start_date (str, optional): Start date in ISO format
        end_date (str, optional): End date in ISO format
    
    Returns:
        dict: API response or error information
    """
    try:
        # Get access token
        access_token = get_ringcx_access_token()
        print(f"✓ Access token obtained: {access_token[:50]}...")
        
        # Create payload
        payload = create_call_report_payload(start_date, end_date)
        
        # Prepare headers exactly as specified
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json"
        }
        
        # Use the exact endpoint from your specification
        url = "https://ringcx.ringcentral.com/voice/api/v1/admin/accounts/********/reportsStreaming"
        
        print(f"\nMaking POST request to: {url}")
        print(f"Headers: {json.dumps(headers, indent=2)}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        # Make the API request
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        # Handle response
        if response.status_code == 200:
            print("✓ SUCCESS: Call report retrieved successfully!")
            return response.json()
        else:
            print(f"✗ ERROR: {response.status_code} - {response.reason}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
                return {"error": error_data, "status_code": response.status_code}
            except:
                print(f"Error text: {response.text}")
                return {"error": response.text, "status_code": response.status_code}
                
    except Exception as e:
        print(f"✗ Exception occurred: {str(e)}")
        return {"error": str(e), "status_code": None}

def test_permissions():
    """Test what permissions the current token has."""
    try:
        access_token = get_ringcx_access_token()
        
        # Test different endpoints to see what's accessible
        test_endpoints = [
            ("GET", "/api/v1/admin/accounts/~"),
            ("GET", "/api/v1/accounts/~"),
            ("GET", "/api/v1/admin/users"),
            ("GET", "/api/v1/admin/accounts/~/agents"),
            ("GET", "/api/v1/admin/accounts/~/campaigns"),
        ]
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }
        
        print("Testing available endpoints...")
        print("=" * 50)
        
        for method, endpoint in test_endpoints:
            url = f"https://ringcx.ringcentral.com{endpoint}"
            try:
                if method == "GET":
                    response = requests.get(url, headers=headers, timeout=10)
                
                print(f"{method} {endpoint}: {response.status_code}")
                if response.status_code == 200:
                    print(f"  ✓ SUCCESS - Available!")
                elif response.status_code == 403:
                    print(f"  ✗ FORBIDDEN - No permission")
                elif response.status_code == 404:
                    print(f"  ? NOT FOUND - Endpoint doesn't exist")
                else:
                    print(f"  ? {response.reason}")
                    
            except Exception as e:
                print(f"{method} {endpoint}: ERROR - {str(e)}")
        
    except Exception as e:
        print(f"Permission test failed: {e}")

def main():
    """Main function to test the RingCX API."""
    print("RingCX Call Report Query Tool")
    print("=" * 50)
    
    # Test 1: Check permissions
    print("\n1. Testing API permissions...")
    test_permissions()
    
    # Test 2: Try the exact query from your specification
    print("\n2. Testing call report query...")
    result = query_ringcx_reports()
    
    # Test 3: Try with custom date
    print("\n3. Testing with custom date range...")
    custom_result = query_ringcx_reports(
        start_date="2025-06-01T00:00:00.000-0000",
        end_date="2025-06-02T00:00:00.000-0000"
    )
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print("=" * 50)
    
    if isinstance(result, dict) and "error" in result:
        print("✗ Call report query failed")
        if result.get("status_code") == 403:
            print("  Issue: 403 Forbidden - Insufficient permissions")
            print("  Solution: Contact RingCentral support to add reporting permissions")
        else:
            print(f"  Error: {result.get('error', 'Unknown error')}")
    else:
        print("✓ Call report query successful!")
        print(f"  Response keys: {list(result.keys()) if isinstance(result, dict) else 'Non-dict response'}")
    
    print("\nNext Steps:")
    print("- If getting 403 errors, contact RingCentral support")
    print("- Request access to RingCX reporting APIs")
    print("- Verify your application has the correct permissions")
    print("- The code framework is ready once permissions are granted")

if __name__ == "__main__":
    main()
