<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-tools"></i> PinFix - Super Admin
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/operators">Operators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/machines">Global Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/reports">Reports</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> Super Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1><i class="bi bi-graph-up"></i> System Reports</h1>
                <p class="text-muted">Comprehensive analytics and insights across the PinFix platform</p>
            </div>
        </div>

        <!-- Error Alert -->
        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <!-- Key Statistics -->
        <div class="row mb-4">
            <div class="col-12">
                <h3><i class="bi bi-speedometer2"></i> Key Statistics</h3>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
                        <h4 class="mt-2"><%= stats.totalIssues %></h4>
                        <p class="text-muted mb-0">Total Issues</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-camera text-info fs-1"></i>
                        <h4 class="mt-2"><%= stats.totalPictures %></h4>
                        <p class="text-muted mb-0">Pictures Uploaded</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-buildings text-primary fs-1"></i>
                        <h4 class="mt-2"><%= stats.totalOperators %></h4>
                        <p class="text-muted mb-0">Operators</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-controller text-success fs-1"></i>
                        <h4 class="mt-2"><%= stats.totalMachines %></h4>
                        <p class="text-muted mb-0">Machines</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-people text-secondary fs-1"></i>
                        <h4 class="mt-2"><%= stats.totalTechs %></h4>
                        <p class="text-muted mb-0">Technicians</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-database text-dark fs-1"></i>
                        <h4 class="mt-2"><%= stats.dbSize %> MB</h4>
                        <p class="text-muted mb-0">Database Size</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="bi bi-clock text-warning fs-1"></i>
                        <h4 class="mt-2"><%= stats.avgResolutionHours %> hours</h4>
                        <p class="text-muted mb-0">Average Resolution Time</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="<%= stats.systemStatusIcon %> text-<%= stats.systemStatusColor %> fs-1"></i>
                        <h4 class="mt-2"><%= stats.systemStatus %></h4>
                        <p class="text-muted mb-0">System Status</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-pie-chart"></i> Issues by Machine Type</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="machineTypeChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-pie-chart"></i> Issue Status Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-graph-up"></i> Issues Created Over Time (Last 30 Days)</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="issuesOverTimeChart" width="800" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 3 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-bar-chart"></i> Weekly Issues (Last 12 Weeks)</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="weeklyChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-bar-chart"></i> Monthly Issues (Last 12 Months)</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Lists -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-trophy"></i> Top Operators by Issues</h5>
                    </div>
                    <div class="card-body">
                        <% if (charts.topOperatorsByIssues && charts.topOperatorsByIssues.length > 0) { %>
                            <% charts.topOperatorsByIssues.forEach((operator, index) => { %>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="badge bg-primary me-2"><%= index + 1 %></span>
                                        <%= operator.name %>
                                    </div>
                                    <span class="badge bg-secondary"><%= operator.issue_count %></span>
                                </div>
                            <% }); %>
                        <% } else { %>
                            <p class="text-muted">No data available</p>
                        <% } %>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-list-ol"></i> Top Issue Types</h5>
                    </div>
                    <div class="card-body">
                        <% if (charts.topIssueTypes && charts.topIssueTypes.length > 0) { %>
                            <% charts.topIssueTypes.slice(0, 5).forEach((issueType, index) => { %>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <span class="badge bg-warning me-2"><%= index + 1 %></span>
                                        <%= issueType.issue_type %>
                                    </div>
                                    <span class="badge bg-secondary"><%= issueType.count %></span>
                                </div>
                            <% }); %>
                        <% } else { %>
                            <p class="text-muted">No data available</p>
                        <% } %>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-person-check"></i> Top Tech Performance</h5>
                    </div>
                    <div class="card-body">
                        <% if (charts.techPerformance && charts.techPerformance.length > 0) { %>
                            <% charts.techPerformance.slice(0, 5).forEach((tech, index) => { %>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span><%= tech.name %></span>
                                        <span class="badge bg-success"><%= tech.resolved_count %> resolved</span>
                                    </div>
                                    <small class="text-muted">
                                        Avg: <%= tech.avg_resolution_hours ? tech.avg_resolution_hours.toFixed(1) : 'N/A' %>h
                                    </small>
                                </div>
                            <% }); %>
                        <% } else { %>
                            <p class="text-muted">No data available</p>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart Data -->
    <script>
        // Pass chart data to global variable for external script
        window.chartData = <%- JSON.stringify(charts) %>;
    </script>

    <!-- Reports JavaScript -->
    <script src="/static/js/reports.js"></script>
