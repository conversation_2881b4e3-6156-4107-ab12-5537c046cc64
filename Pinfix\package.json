{"name": "pinfix-repair", "version": "1.0.0", "description": "Pinball issue tracking web application for multiple operators", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "init-db": "node scripts/init-database.js"}, "keywords": ["pinball", "issue-tracking", "repair", "arcade"], "author": "Pinfix Repair", "license": "MIT", "dependencies": {"@sendgrid/mail": "^7.7.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.10", "express": "^4.18.2", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "qrcode": "^1.5.3", "resend": "^6.0.1", "sharp": "^0.32.6", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}