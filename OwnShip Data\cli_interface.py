"""
Command Line Interface for Own Ship Extractor
Supports automated operation for Windows Task Scheduler
"""

import argparse
import os
import sys
import datetime
from pathlib import Path

from database_manager import DatabaseManager
from data_extractor import DataExtractor


def get_default_output_folder():
    """Get default output folder (user's Documents folder)"""
    try:
        # Try to get Documents folder
        import winreg
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                           r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders") as key:
            documents_path = winreg.QueryValueEx(key, "Personal")[0]
            return documents_path
    except:
        # Fallback to user home directory
        return str(Path.home())


def generate_output_filename(output_folder, use_local_time=False):
    """Generate timestamped filename for CSV output"""
    now = datetime.datetime.now()
    if not use_local_time:
        now = datetime.datetime.utcnow()
    
    timestamp = now.strftime("%Y%m%d_%H%M%S")
    timezone_suffix = "Local" if use_local_time else "UTC"
    filename = f"OwnShipData_{timestamp}_{timezone_suffix}.csv"
    
    return os.path.join(output_folder, filename)


def validate_database_path(db_path):
    """Validate the provided database path"""
    if not os.path.exists(db_path):
        return False, f"Database file not found: {db_path}"
    
    if not db_path.lower().endswith('.tzdb'):
        return False, f"File is not a .tzdb file: {db_path}"
    
    # Use DatabaseManager to validate
    db_manager = DatabaseManager()
    is_valid, error_msg = db_manager.validate_database(db_path)
    
    return is_valid, error_msg


def extract_and_export(db_path, output_folder, hours_back, use_local_time):
    """Extract data and export to CSV"""
    try:
        # Validate database
        is_valid, error_msg = validate_database_path(db_path)
        if not is_valid:
            print(f"Error: {error_msg}")
            return False
        
        # Create output folder if it doesn't exist
        os.makedirs(output_folder, exist_ok=True)
        
        # Initialize data extractor
        extractor = DataExtractor(db_path)
        
        # Extract data
        print(f"Extracting data from last {hours_back} hours...")
        data = extractor.get_data_for_timeframe(hours_back)
        
        if not data:
            print("Warning: No data found for the specified time range")
            return False
        
        # Convert to DataFrame
        df = extractor.to_dataframe(data, use_local_time)
        
        # Generate output filename
        output_file = generate_output_filename(output_folder, use_local_time)
        
        # Export to CSV
        df.to_csv(output_file, index=False)
        
        print(f"Success: Exported {len(data)} records to {output_file}")
        return True
        
    except Exception as e:
        print(f"Error during extraction: {str(e)}")
        return False


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Own Ship Extractor - Command Line Interface",
        epilog="""
Examples:
  python cli_interface.py C:\\Data\\OwnShipRecorder.tzdb
  python cli_interface.py C:\\Data\\OwnShipRecorder.tzdb --output C:\\Exports --hours 48 --local
  python cli_interface.py --gui
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        'database_path',
        nargs='?',
        help='Path to OwnShipRecorder.tzdb file (if not provided, GUI will open)'
    )
    
    parser.add_argument(
        '--output', '-o',
        default=None,
        help=f'Output folder for CSV file (default: {get_default_output_folder()})'
    )
    
    parser.add_argument(
        '--hours', '-t',
        type=int,
        default=24,
        help='Time frame in hours from the last entry (default: 24)'
    )
    
    parser.add_argument(
        '--local', '-l',
        action='store_true',
        help='Use local time instead of UTC (default: UTC)'
    )
    
    parser.add_argument(
        '--gui', '-g',
        action='store_true',
        help='Force open GUI interface'
    )
    
    parser.add_argument(
        '--detect', '-d',
        action='store_true',
        help='Detect available databases and exit'
    )
    
    args = parser.parse_args()
    
    # Handle detection mode
    if args.detect:
        print("Detecting OwnShipRecorder databases...")
        db_manager = DatabaseManager()
        detected = db_manager.detect_databases()
        
        if detected:
            print(f"Found {len(detected)} database(s):")
            for display_name, path in detected:
                print(f"  {display_name}: {path}")
                info = db_manager.get_database_info(path)
                if info['valid']:
                    print(f"    Records: {info['record_count']:,}")
                    if info['date_range'][0]:
                        extractor = DataExtractor(path)
                        start_date, end_date = extractor.get_date_range()
                        print(f"    Date Range: {start_date} to {end_date}")
                else:
                    print(f"    Error: {info['error']}")
        else:
            print("No databases found in standard locations")
        
        return 0
    
    # Handle GUI mode or no database path provided
    if args.gui or not args.database_path:
        print("Opening GUI interface...")
        try:
            from own_ship_extractor import main as gui_main
            gui_main()
            return 0
        except ImportError as e:
            print(f"Error: Could not import GUI components: {e}")
            print("Make sure all required packages are installed (tkinter, matplotlib, pandas)")
            return 1
        except Exception as e:
            print(f"Error starting GUI: {e}")
            return 1
    
    # Command line mode
    db_path = args.database_path
    output_folder = args.output or get_default_output_folder()
    hours_back = args.hours
    use_local_time = args.local
    
    print(f"Own Ship Extractor - Command Line Mode")
    print(f"Database: {db_path}")
    print(f"Output folder: {output_folder}")
    print(f"Time frame: {hours_back} hours")
    print(f"Time zone: {'Local' if use_local_time else 'UTC'}")
    print()
    
    # Validate parameters
    if hours_back <= 0:
        print("Error: Hours must be a positive number")
        return 1
    
    # Extract and export
    success = extract_and_export(db_path, output_folder, hours_back, use_local_time)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
