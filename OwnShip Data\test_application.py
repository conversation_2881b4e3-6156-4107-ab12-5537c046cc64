"""
Test script for Own Ship Extractor application
Verifies all major functionality
"""

import os
import sys
import tempfile
import datetime
from pathlib import Path

# Import our modules
from database_manager import DatabaseManager
from data_extractor import DataExtractor
from cli_interface import validate_database_path, extract_and_export


def test_database_manager():
    """Test database detection and validation"""
    print("Testing Database Manager...")
    
    db_manager = DatabaseManager()
    
    # Test detection
    detected = db_manager.detect_databases()
    print(f"  Detected {len(detected)} databases")
    
    # Test validation with current database
    if os.path.exists("OwnShipRecorder.tzdb"):
        is_valid, error_msg = db_manager.validate_database("OwnShipRecorder.tzdb")
        print(f"  Current database valid: {is_valid}")
        if not is_valid:
            print(f"    Error: {error_msg}")
        
        # Test database info
        info = db_manager.get_database_info("OwnShipRecorder.tzdb")
        if info['valid']:
            print(f"    Records: {info['record_count']:,}")
            print(f"    Date range: {info['date_range']}")
    
    print("  Database Manager: PASSED\n")


def test_data_extractor():
    """Test data extraction and conversion"""
    print("Testing Data Extractor...")
    
    if not os.path.exists("OwnShipRecorder.tzdb"):
        print("  No test database available, skipping...")
        return
    
    extractor = DataExtractor("OwnShipRecorder.tzdb")
    
    # Test date range
    start_date, end_date = extractor.get_date_range()
    print(f"  Date range: {start_date} to {end_date}")
    
    # Test data extraction (small sample)
    records = extractor.extract_data()[:100]  # First 100 records
    print(f"  Extracted {len(records)} sample records")
    
    if records:
        # Test data conversion
        first_record = records[0]
        print(f"    First record: {first_record.datetime}")
        print(f"    Position: {first_record.latitude}, {first_record.longitude}")
        print(f"    Speed: {first_record.speed_over_ground} kn")
        
        # Test DataFrame conversion
        df = extractor.to_dataframe(records[:10])
        print(f"    DataFrame shape: {df.shape}")
        
        # Test timeframe extraction
        timeframe_data = extractor.get_data_for_timeframe(1)  # Last hour
        print(f"    Last hour data: {len(timeframe_data)} records")
    
    print("  Data Extractor: PASSED\n")


def test_cli_interface():
    """Test command-line interface functionality"""
    print("Testing CLI Interface...")
    
    if not os.path.exists("OwnShipRecorder.tzdb"):
        print("  No test database available, skipping...")
        return
    
    # Test database validation
    is_valid, error_msg = validate_database_path("OwnShipRecorder.tzdb")
    print(f"  Database validation: {is_valid}")
    
    if is_valid:
        # Test extraction to temporary folder
        with tempfile.TemporaryDirectory() as temp_dir:
            success = extract_and_export(
                "OwnShipRecorder.tzdb", 
                temp_dir, 
                1,  # 1 hour
                False  # UTC
            )
            print(f"  CLI extraction: {'PASSED' if success else 'FAILED'}")
            
            # Check if file was created
            csv_files = list(Path(temp_dir).glob("*.csv"))
            if csv_files:
                csv_file = csv_files[0]
                file_size = csv_file.stat().st_size
                print(f"    Created CSV: {csv_file.name} ({file_size:,} bytes)")
    
    print("  CLI Interface: PASSED\n")


def test_gui_imports():
    """Test that GUI components can be imported"""
    print("Testing GUI Imports...")
    
    try:
        import tkinter as tk
        print("  tkinter: Available")
        
        import matplotlib.pyplot as plt
        print("  matplotlib: Available")
        
        import pandas as pd
        print("  pandas: Available")
        
        # Test importing our GUI module
        from own_ship_extractor import OwnShipExtractorGUI
        print("  GUI module: Available")
        
        print("  GUI Imports: PASSED\n")
        
    except ImportError as e:
        print(f"  GUI Imports: FAILED - {e}\n")


def test_file_structure():
    """Test that all required files are present"""
    print("Testing File Structure...")
    
    required_files = [
        "database_manager.py",
        "data_extractor.py", 
        "cli_interface.py",
        "own_ship_extractor.py",
        "extract_ownship_data.bat",
        "README.md"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  {file}: Present")
        else:
            print(f"  {file}: MISSING")
            missing_files.append(file)
    
    if missing_files:
        print(f"  File Structure: FAILED - Missing {len(missing_files)} files")
    else:
        print("  File Structure: PASSED\n")


def main():
    """Run all tests"""
    print("Own Ship Extractor - Test Suite")
    print("=" * 40)
    
    test_file_structure()
    test_gui_imports()
    test_database_manager()
    test_data_extractor()
    test_cli_interface()
    
    print("Test suite completed!")
    print("\nTo test the GUI manually, run:")
    print("  python own_ship_extractor.py")
    print("\nTo test CLI manually, run:")
    print("  python cli_interface.py --detect")


if __name__ == "__main__":
    main()
