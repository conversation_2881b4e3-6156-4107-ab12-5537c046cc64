// Login page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Operator form validation
    const operatorForm = document.getElementById('operatorForm');
    if (operatorForm) {
        operatorForm.addEventListener('submit', function(e) {
            const operatorAccount = document.getElementById('operatorAccount').value.trim();
            if (!operatorAccount) {
                e.preventDefault();
                alert('Please enter an operator account name');
                return false;
            }
            // Let the form submit normally to the POST route
            // The JavaScript redirect is kept as a fallback
        });
    }
});
