# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_PATH=./data/pinfix.db

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# SendGrid Configuration
SENDGRID_API_KEY=your-sendgrid-api-key-here
FROM_EMAIL=<EMAIL>

# Super Admin Configuration
SUPER_ADMIN_USERNAME=superadmin
SUPER_ADMIN_PASSWORD=190880

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Application Configuration
BASE_URL=http://localhost:3000
