apiVersion: apps/v1
kind: Deployment
metadata:
  name: call-analysis
  labels:
    app: call-analysis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: call-analysis
  template:
    metadata:
      labels:
        app: call-analysis
    spec:
      containers:
      - name: call-analysis
        image: call-analysis:latest  # Replace with your actual image
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5000
          name: http
        env:
        # Environment variables from ConfigMap
        - name: FLASK_ENV
          valueFrom:
            configMapKeyRef:
              name: call-analysis-config
              key: FLASK_ENV
        - name: FLASK_APP
          valueFrom:
            configMapKeyRef:
              name: call-analysis-config
              key: FLASK_APP
        - name: RINGCX_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: call-analysis-config
              key: RINGCX_BASE_URL
        - name: DEFAULT_DAYS
          valueFrom:
            configMapKeyRef:
              name: call-analysis-config
              key: DEFAULT_DAYS
        
        # Environment variables from Secrets
        - name: ASSEMBLYAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: call-analysis-secrets
              key: ASSEMBLYAI_API_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: call-analysis-secrets
              key: OPENAI_API_KEY
        - name: RC_CREDENTIALS_JSON
          valueFrom:
            secretKeyRef:
              name: call-analysis-secrets
              key: RC_CREDENTIALS_JSON
        
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: reports-volume
          mountPath: /app/reports
        - name: api-keys
          mountPath: /app/keys
          readOnly: true
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: call-analysis-data
      - name: reports-volume
        persistentVolumeClaim:
          claimName: call-analysis-reports
      - name: api-keys
        secret:
          secretName: call-analysis-secrets
      
      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      
      # Restart policy
      restartPolicy: Always
