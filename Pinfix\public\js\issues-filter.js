// Issues filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss alerts after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert-danger, .alert-success, .alert-warning');
        alerts.forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Filter functionality
    let activeFilter = null;

    // Add click handlers to filter cards
    document.querySelectorAll('.filter-card').forEach(card => {
        card.addEventListener('click', function() {
            const status = this.getAttribute('data-status');
            const assignedTo = this.getAttribute('data-assigned-to');
            const filterValue = status || assignedTo;

            console.log('Card clicked:', filterValue); // Debug log

            if (activeFilter === filterValue) {
                // If clicking the same filter, clear it
                clearFilters();
            } else {
                // Apply new filter
                applyFilter(filterValue, assignedTo ? 'assigned-to' : 'status');
            }
        });
    });

    // Clear filters button
    const clearButton = document.getElementById('clear-filters');
    if (clearButton) {
        clearButton.addEventListener('click', clearFilters);
    }

    function applyFilter(filterValue, filterType = 'status') {
        console.log('Applying filter:', filterValue, 'type:', filterType); // Debug log
        activeFilter = filterValue;

        // Update card appearances
        document.querySelectorAll('.filter-card').forEach(card => {
            const cardStatus = card.getAttribute('data-status');
            const cardAssignedTo = card.getAttribute('data-assigned-to');
            const cardValue = cardStatus || cardAssignedTo;

            if (cardValue === filterValue) {
                card.style.boxShadow = '0 0 0 3px rgba(13, 110, 253, 0.25)';
                card.style.borderColor = '#0d6efd';
            } else {
                card.style.boxShadow = '';
                card.style.borderColor = '';
                card.style.opacity = '0.6';
            }
        });

        // Filter table rows
        let visibleCount = 0;
        document.querySelectorAll('.issue-row').forEach(row => {
            let shouldShow = false;

            if (filterType === 'status') {
                shouldShow = row.getAttribute('data-status') === filterValue;
            } else if (filterType === 'assigned-to') {
                shouldShow = row.getAttribute('data-assigned-to') === filterValue;
            }

            if (shouldShow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        console.log('Visible rows:', visibleCount); // Debug log

        // Show filter status and clear button
        const filterStatus = document.getElementById('filter-status');
        const clearButton = document.getElementById('clear-filters');

        if (filterStatus) {
            filterStatus.textContent = `Showing: ${getStatusDisplayName(filterValue, filterType)}`;
            filterStatus.style.display = 'inline';
        }
        if (clearButton) {
            clearButton.style.display = 'inline-block';
        }
    }

    function clearFilters() {
        console.log('Clearing filters'); // Debug log
        activeFilter = null;
        
        // Reset card appearances
        document.querySelectorAll('.filter-card').forEach(card => {
            card.style.boxShadow = '';
            card.style.borderColor = '';
            card.style.opacity = '';
        });

        // Show all table rows
        document.querySelectorAll('.issue-row').forEach(row => {
            row.style.display = '';
        });

        // Hide filter status and clear button
        const filterStatus = document.getElementById('filter-status');
        const clearButton = document.getElementById('clear-filters');
        
        if (filterStatus) {
            filterStatus.style.display = 'none';
        }
        if (clearButton) {
            clearButton.style.display = 'none';
        }
    }

    function getStatusDisplayName(value, filterType = 'status') {
        if (filterType === 'assigned-to') {
            switch(value) {
                case 'operator': return 'My Issues';
                default: return value;
            }
        } else {
            switch(value) {
                case 'Open': return 'Open Issues';
                case 'Assigned': return 'In Progress';
                case 'Not Duplicated': return 'Not Duplicated';
                case 'Resolved': return 'Resolved';
                default: return value;
            }
        }
    }
});
