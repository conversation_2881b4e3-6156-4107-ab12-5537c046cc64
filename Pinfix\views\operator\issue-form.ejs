<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/profile">Profile</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-exclamation-triangle"></i> Report Issue
                    </h1>
                    <a href="/<%= operator.username %>/admin/machines" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Machines
                    </a>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Issue Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Issue Details</h5>
                    </div>
                    <div class="card-body">
                        <!-- Machine Information -->
                        <% if (machine) { %>
                            <div class="card bg-info bg-opacity-10 border-info mb-4">
                                <div class="card-body">
                                    <h6 class="mb-2 text-info"><i class="bi bi-controller"></i> Machine: <%= machine.name %></h6>
                                    <% if (machine.location_notes) { %>
                                        <small class="text-muted"><i class="bi bi-geo-alt"></i> Location: <%= machine.location_notes %></small>
                                    <% } %>
                                </div>
                            </div>
                        <% } else { %>
                            <div class="card bg-warning bg-opacity-10 border-warning mb-4">
                                <div class="card-body">
                                    <h6 class="mb-2 text-warning"><i class="bi bi-exclamation-triangle"></i> Machine Information Missing</h6>
                                    <small class="text-muted">Please select a machine from the machines list to report an issue.</small>
                                </div>
                            </div>
                        <% } %>

                        <form method="POST" action="/<%= operator.username %>/admin/issues">
                            <input type="hidden" name="machine_id" value="<%= machine ? machine.id : '' %>">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="issue_type" class="form-label">Issue Type *</label>
                                    <select class="form-select" id="issue_type" name="issue_type" required>
                                        <option value="">Select issue type...</option>
                                        <option value="Flipper not working" <%= issue && issue.issue_type === 'Flipper not working' ? 'selected' : '' %>>Flipper not working</option>
                                        <option value="Flipper weak or Sticking" <%= issue && issue.issue_type === 'Flipper weak or Sticking' ? 'selected' : '' %>>Flipper weak or Sticking</option>
                                        <option value="Ball not ejecting" <%= issue && issue.issue_type === 'Ball not ejecting' ? 'selected' : '' %>>Ball not ejecting</option>
                                        <option value="Ball Stuck on Playfield" <%= issue && issue.issue_type === 'Ball Stuck on Playfield' ? 'selected' : '' %>>Ball Stuck on Playfield</option>
                                        <option value="Pop bumper not firing" <%= issue && issue.issue_type === 'Pop bumper not firing' ? 'selected' : '' %>>Pop bumper not firing</option>
                                        <option value="Slingshot not firing" <%= issue && issue.issue_type === 'Slingshot not firing' ? 'selected' : '' %>>Slingshot not firing</option>
                                        <option value="Switch not registering hits" <%= issue && issue.issue_type === 'Switch not registering hits' ? 'selected' : '' %>>Switch not registering hits</option>
                                        <option value="Drop target not resetting" <%= issue && issue.issue_type === 'Drop target not resetting' ? 'selected' : '' %>>Drop target not resetting</option>
                                        <option value="General illumination out" <%= issue && issue.issue_type === 'General illumination out' ? 'selected' : '' %>>General illumination out</option>
                                        <option value="Feature lamp not working" <%= issue && issue.issue_type === 'Feature lamp not working' ? 'selected' : '' %>>Feature lamp not working</option>
                                        <option value="Vertical Up-Kicker not working or weak" <%= issue && issue.issue_type === 'Vertical Up-Kicker not working or weak' ? 'selected' : '' %>>Vertical Up-Kicker not working or weak</option>
                                        <option value="Sound distorted" <%= issue && issue.issue_type === 'Sound distorted' ? 'selected' : '' %>>Sound distorted</option>
                                    </select>
                                    <div class="form-text">What type of issue is this?</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="priority" class="form-label">Priority *</label>
                                    <select class="form-select" id="priority" name="priority" required>
                                        <option value="">Select priority...</option>
                                        <option value="Low" <%= issue && issue.priority === 'Low' ? 'selected' : '' %>>Low - Minor issue, machine playable</option>
                                        <option value="Medium" <%= issue && issue.priority === 'Medium' ? 'selected' : '' %>>Medium - Affects gameplay</option>
                                        <option value="High" <%= issue && issue.priority === 'High' ? 'selected' : '' %>>High - Machine unplayable</option>
                                    </select>
                                    <div class="form-text">How urgent is this issue?</div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="description" class="form-label">Additional Notes</label>
                                <textarea class="form-control"
                                          id="description"
                                          name="description"
                                          rows="3"
                                          placeholder="Optional: Any additional details about the issue, specific location on playfield, etc."><%= issue ? (issue.description || '') : '' %></textarea>
                                <div class="form-text">Optional: Additional details that might help with diagnosis</div>
                            </div>

                            <!-- Report Source Info -->
                            <div class="card bg-success bg-opacity-10 border-success mb-4">
                                <div class="card-body">
                                    <span class="text-success"><i class="bi bi-person-badge"></i> <strong>Reported by:</strong> Internal (Operator Admin)</span>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="/<%= operator.username %>/admin/machines/<%= machine.id %>" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-exclamation-triangle"></i> Report Issue
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Issue Form JS -->
    <script src="/static/js/issue-form.js"></script>
</body>
</html>
