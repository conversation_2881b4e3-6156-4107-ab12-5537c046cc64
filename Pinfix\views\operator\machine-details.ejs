<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-controller"></i> <%= machine.name %></h1>
                    <a href="/<%= operator.username %>/admin/machines" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Machines
                    </a>
                </div>
            </div>
        </div>

        <!-- Machine Information -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Machine Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Basic Details</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td><%= machine.name %></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Manufacturer:</strong></td>
                                        <td>
                                            <span class="badge bg-secondary"><%= machine.manufacturer %></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Year:</strong></td>
                                        <td><%= machine.date_of_manufacture || 'Unknown' %></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Type:</strong></td>
                                        <td>
                                            <span class="badge bg-secondary"><%= machine.machine_type %></span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Location & Status</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-<%= machine.status === 'active' ? 'success' : 'secondary' %>">
                                                <%= machine.status %>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Location:</strong></td>
                                        <td><%= machine.location_notes || 'Not specified' %></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Added:</strong></td>
                                        <td><%= machine.formatted_created_date %></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Open Issues:</strong></td>
                                        <td>
                                            <%
                                            let badgeClass = 'bg-success'; // Green for 0 issues
                                            if (openIssuesCount === 1) badgeClass = 'bg-warning'; // Yellow for 1 issue
                                            else if (openIssuesCount >= 2) badgeClass = 'bg-danger'; // Red for 2+ issues
                                            %>
                                            <span class="badge <%= badgeClass %>">
                                                <%= openIssuesCount %>
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Issues -->
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Issues</h5>
                        <% if (recentIssues.length > 0) { %>
                            <div>
                                <span id="filter-status" class="badge bg-info me-2" style="display: none;">Showing: Not Duplicated</span>
                                <button id="filter-not-duplicated" class="btn btn-sm btn-outline-warning">
                                    <i class="bi bi-funnel"></i> Show Not Duplicated Only
                                </button>
                            </div>
                        <% } %>
                    </div>
                    <div class="card-body">
                        <% if (recentIssues.length === 0) { %>
                            <div class="text-center py-4">
                                <i class="bi bi-check-circle display-4 text-success"></i>
                                <h6 class="text-muted mt-3">No Issues Reported</h6>
                                <p class="text-muted">This machine has no reported issues. Great job!</p>
                            </div>
                        <% } else { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Issue Type</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Reported</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% recentIssues.forEach(issue => { %>
                                            <tr class="issue-row" data-status="<%= issue.status %>">
                                                <td>
                                                    <%= issue.issue_type %>
                                                    <% if (issue.user_comment) { %>
                                                        <br><small class="text-muted"><%= issue.user_comment.substring(0, 100) %><%= issue.user_comment.length > 100 ? '...' : '' %></small>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <%
                                                    let priorityClass = 'bg-secondary';
                                                    if (issue.priority === 'High') priorityClass = 'bg-danger';
                                                    else if (issue.priority === 'Medium') priorityClass = 'bg-warning';
                                                    else if (issue.priority === 'Low') priorityClass = 'bg-success';
                                                    %>
                                                    <span class="badge <%= priorityClass %>"><%= issue.priority %></span>
                                                </td>
                                                <td>
                                                    <%
                                                    let statusClass = 'bg-warning'; // Default yellow for everything else
                                                    if (issue.status === 'Open') statusClass = 'bg-danger';
                                                    else if (issue.status === 'Resolved') statusClass = 'bg-success';
                                                    %>
                                                    <span class="badge <%= statusClass %>"><%= issue.status.replace('_', ' ') %></span>
                                                </td>
                                                <td>
                                                    <%= issue.formatted_created_date %>
                                                    <br><small class="text-muted"><%= issue.formatted_created_time %></small>
                                                </td>
                                                <td>
                                                    <a href="/<%= operator.username %>/admin/issues/<%= issue.id %>" 
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="bi bi-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>

            <!-- QR Code & Actions -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">QR Code</h5>
                    </div>
                    <div class="card-body text-center">
                        <% if (machine.qr_code_path) { %>
                            <img src="<%= machine.qr_code_path %>" 
                                 alt="QR Code for <%= machine.name %>" 
                                 class="img-fluid mb-3"
                                 style="max-width: 200px;">
                            <p class="text-muted small">
                                Customers can scan this QR code to report issues with this machine.
                            </p>
                            <div class="d-grid gap-2">
                                <% if (machine.status === 'active') { %>
                                    <a href="<%= machine.qr_code_path %>"
                                       download="qr-code-<%= machine.name.replace(/\s+/g, '-').toLowerCase() %>.png"
                                       class="btn btn-success">
                                        <i class="bi bi-download"></i> Download QR Code
                                    </a>
                                <% } else { %>
                                    <button class="btn btn-secondary disabled" disabled>
                                        <i class="bi bi-download"></i> Download QR Code
                                    </button>
                                    <small class="text-muted mt-2">
                                        <i class="bi bi-info-circle"></i> Reactivate machine to enable QR code download
                                    </small>
                                <% } %>
                            </div>
                        <% } else { %>
                            <i class="bi bi-qr-code display-4 text-muted"></i>
                            <p class="text-muted mt-3">QR Code not available</p>
                        <% } %>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <% if (machine.status === 'active') { %>
                                <a href="/<%= operator.username %>/admin/issues/new?machine_id=<%= machine.id %>"
                                   class="btn btn-warning">
                                    <i class="bi bi-exclamation-triangle"></i> Report Issue
                                </a>
                                <a href="/<%= operator.username %>/admin/machines/<%= machine.id %>/edit"
                                   class="btn btn-outline-primary">
                                    <i class="bi bi-pencil"></i> Edit Location
                                </a>
                            <% } else { %>
                                <button class="btn btn-secondary disabled" disabled>
                                    <i class="bi bi-exclamation-triangle"></i> Report Issue
                                </button>
                                <button class="btn btn-outline-secondary disabled" disabled>
                                    <i class="bi bi-pencil"></i> Edit Location
                                </button>
                                <div class="alert alert-warning mt-3">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Machine Decommissioned:</strong> Reactivate this machine to report issues or edit its location.
                                </div>
                            <% } %>
                            <% if (machine.status === 'active') { %>
                                <form method="POST" action="/<%= operator.username %>/admin/machines/<%= machine.id %>/decommission">
                                    <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to decommission this machine? This will mark it as archived but keep all data and QR codes.')">
                                        <i class="bi bi-trash"></i> Decommission Machine
                                    </button>
                                </form>
                            <% } else if (machine.status === 'archived') { %>
                                <form method="POST" action="/<%= operator.username %>/admin/machines/<%= machine.id %>/reactivate">
                                    <button type="submit" class="btn btn-outline-success w-100" onclick="return confirm('Are you sure you want to reactivate this machine?')">
                                        <i class="bi bi-arrow-clockwise"></i> Reactivate Machine
                                    </button>
                                </form>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
    <!-- Machine Details JS -->
    <script src="/static/js/machine-details.js"></script>
</body>
</html>
