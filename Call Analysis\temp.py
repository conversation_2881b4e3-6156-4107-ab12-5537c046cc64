import requests

RC_ACCESS_TOKEN = "SUFEMDFQMTRQQVMwMHxBQURPOGtJdWVzUWdTNy0tY3UxeFM4YmZSZGYyQjZCRWg3WVBYOXlmSWZOdi1IbjlBTndrNkVrQ0FGQ0ZZMnY2RXM3aXBPREVGTnNnM1F2b3RwZkF5NmpYVHZpLXBpa1lla0xlMloyWkFiMXRhUnk5dVBfOTgtWDctbU93R2dwdXY3Wjc3T1JQYWRtUHpVT3FibkVCbzU3ZElFZ3J6UGwzc3hkZGlNS2pndHZyVk83TGhfdjRhZVNSY1l3OWw0NVk1OFE2UTdBaEVXM0dwc0J3RVJmUURwMzFONmpXb3d8T1VJYkZBfE1jdDR6bjNtQlZZbktPemdOYUFBNWd8QVF8QUF8QUFBQUFPQ1RENjg"
url = "https://ringcx.ringcentral.com/api/auth/login/rc/accesstoken"
body = "rcAccessToken=%s&rcTokenType=Bearer" % (RC_ACCESS_TOKEN)
headers = {
              'Content-Type': 'application/x-www-form-urlencoded'
          }
try:
    res = requests.post(url, headers=headers, data=body)
    if res.status_code == 200:
        jsonObj = json.loads(res._content)
        print (jsonObj['accessToken'])
    else:
        print (res._content)
except Exception as e:
    raise ValueError(e)