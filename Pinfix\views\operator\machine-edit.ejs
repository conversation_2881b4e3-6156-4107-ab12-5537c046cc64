<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/profile">Profile</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-pencil"></i> Edit <%= machine.name %>
                    </h1>
                    <a href="/<%= operator.username %>/admin/machines/<%= machine.id %>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Details
                    </a>
                </div>
            </div>
        </div>

        <!-- Info Alert -->
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info alert-permanent" role="alert">
                    <i class="bi bi-info-circle"></i>
                    <strong>Note:</strong> You can only edit the location notes for this machine.
                    The machine details (name, manufacturer, etc.) are managed in the global database.
                </div>
            </div>
        </div>

        <!-- Success Messages -->
        <% if (typeof success !== 'undefined' && success) { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle"></i>
                        <%= success %>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Error Messages -->
        <% if (typeof error !== 'undefined' && error) { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle"></i>
                        <%= error %>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Machine Edit Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Machine Information (Read-only) -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Machine Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <%= machine.name %></p>
                                <p><strong>Manufacturer:</strong> 
                                    <span class="badge bg-secondary"><%= machine.manufacturer %></span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Year:</strong> <%= machine.date_of_manufacture || 'Unknown' %></p>
                                <p><strong>Type:</strong> 
                                    <% 
                                    let typeClass = 'bg-info';
                                    if (machine.machine_type === 'EM') typeClass = 'bg-warning';
                                    else if (machine.machine_type === 'SS Alpha') typeClass = 'bg-primary';
                                    else if (machine.machine_type === 'SS DMD') typeClass = 'bg-success';
                                    else if (machine.machine_type === 'SS LCD') typeClass = 'bg-danger';
                                    %>
                                    <span class="badge <%= typeClass %>"><%= machine.machine_type %></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Editable Fields -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Location Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/<%= operator.username %>/admin/machines/<%= machine.id %>/edit">
                            <div class="mb-4">
                                <label for="location_notes" class="form-label">Location Notes</label>
                                <textarea class="form-control" 
                                          id="location_notes" 
                                          name="location_notes" 
                                          rows="4"
                                          placeholder="e.g., Main floor near entrance, Upstairs arcade section, etc."><%= machine.location_notes || '' %></textarea>
                                <div class="form-text">
                                    Optional notes about where this machine is located in your venue. 
                                    This helps technicians find the machine quickly when responding to issues.
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="/<%= operator.username %>/admin/machines/<%= machine.id %>" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Update Location
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
</body>
</html>
