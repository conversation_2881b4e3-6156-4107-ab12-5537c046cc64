// Machine details page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality for Recent Issues
    let isFilterActive = false;
    
    const filterButton = document.getElementById('filter-not-duplicated');
    const filterStatus = document.getElementById('filter-status');
    
    if (filterButton) {
        filterButton.addEventListener('click', function() {
            if (isFilterActive) {
                // Clear filter - show all issues
                clearFilter();
            } else {
                // Apply filter - show only "Not Duplicated" issues
                applyNotDuplicatedFilter();
            }
        });
    }
    
    function applyNotDuplicatedFilter() {
        isFilterActive = true;
        
        // Update button appearance
        filterButton.innerHTML = '<i class="bi bi-funnel-fill"></i> Show All Issues';
        filterButton.classList.remove('btn-outline-warning');
        filterButton.classList.add('btn-warning');
        
        // Filter table rows
        let visibleCount = 0;
        document.querySelectorAll('.issue-row').forEach(row => {
            if (row.getAttribute('data-status') === 'Not Duplicated') {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        // Show filter status
        if (filterStatus) {
            filterStatus.style.display = 'inline';
        }
        
        // Show message if no "Not Duplicated" issues found
        if (visibleCount === 0) {
            showNoResultsMessage();
        } else {
            hideNoResultsMessage();
        }
    }
    
    function clearFilter() {
        isFilterActive = false;
        
        // Reset button appearance
        filterButton.innerHTML = '<i class="bi bi-funnel"></i> Show Not Duplicated Only';
        filterButton.classList.remove('btn-warning');
        filterButton.classList.add('btn-outline-warning');
        
        // Show all table rows
        document.querySelectorAll('.issue-row').forEach(row => {
            row.style.display = '';
        });
        
        // Hide filter status
        if (filterStatus) {
            filterStatus.style.display = 'none';
        }
        
        // Hide no results message
        hideNoResultsMessage();
    }
    
    function showNoResultsMessage() {
        // Check if message already exists
        if (document.getElementById('no-not-duplicated-message')) {
            return;
        }
        
        const tbody = document.querySelector('.issue-row').closest('tbody');
        const messageRow = document.createElement('tr');
        messageRow.id = 'no-not-duplicated-message';
        messageRow.innerHTML = `
            <td colspan="5" class="text-center py-4">
                <i class="bi bi-info-circle display-6 text-muted"></i>
                <h6 class="text-muted mt-3">No "Not Duplicated" Issues</h6>
                <p class="text-muted">This machine has no issues marked as "Not Duplicated".</p>
            </td>
        `;
        tbody.appendChild(messageRow);
    }
    
    function hideNoResultsMessage() {
        const messageRow = document.getElementById('no-not-duplicated-message');
        if (messageRow) {
            messageRow.remove();
        }
    }
});
