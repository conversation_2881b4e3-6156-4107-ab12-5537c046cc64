<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix - Super Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/operators">Operators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/machines">Global Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/reports">Reports</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> Super Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-buildings-fill"></i> Manage Operators</h1>
                    <a href="/admin/operators/new" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Create New Operator
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <% if (typeof success !== 'undefined' || typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <% if (typeof success !== 'undefined') { %>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <%= success %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    <% if (typeof error !== 'undefined') { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } %>

        <!-- Operators Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Operators</h5>
                    </div>
                    <div class="card-body">
                        <% if (operators.length === 0) { %>
                            <div class="text-center py-4">
                                <i class="bi bi-buildings display-1 text-muted"></i>
                                <h4 class="text-muted mt-3">No Operators Found</h4>
                                <p class="text-muted">Create your first operator to get started.</p>
                                <a href="/admin/operators/new" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Create First Operator
                                </a>
                            </div>
                        <% } else { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Username</th>
                                            <th>Status</th>
                                            <th>Machines</th>
                                            <th>Issues</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% operators.forEach(operator => { %>
                                            <tr>
                                                <td>
                                                    <strong><%= operator.name %></strong>
                                                    <% if (operator.phone) { %>
                                                        <br><small class="text-muted"><%= operator.phone %></small>
                                                    <% } %>
                                                </td>
                                                <td><%= operator.email %></td>
                                                <td><%= operator.username %></td>
                                                <td>
                                                    <% if (operator.status === 'active') { %>
                                                        <span class="badge bg-success">Active</span>
                                                    <% } else { %>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><%= operator.machine_count || 0 %></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning"><%= operator.issue_count || 0 %></span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <%= new Date(operator.created_at).toLocaleDateString() %>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="/admin/operators/<%= operator.id %>/edit" 
                                                           class="btn btn-outline-primary" title="Edit">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <form method="POST" action="/admin/operators/<%= operator.id %>/toggle-status" 
                                                              class="d-inline" 
                                                              onsubmit="return confirm('Are you sure you want to <%= operator.status === 'active' ? 'deactivate' : 'activate' %> this operator?')">
                                                            <button type="submit" 
                                                                    class="btn btn-outline-<%= operator.status === 'active' ? 'warning' : 'success' %>" 
                                                                    title="<%= operator.status === 'active' ? 'Deactivate' : 'Activate' %>">
                                                                <i class="bi bi-<%= operator.status === 'active' ? 'pause' : 'play' %>"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>

    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Handle URL parameters for success/error messages
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('success')) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${urlParams.get('success')}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('main .container-fluid').insertBefore(alertDiv, document.querySelector('main .container-fluid').firstChild);
        }
        if (urlParams.get('error')) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${urlParams.get('error')}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('main .container-fluid').insertBefore(alertDiv, document.querySelector('main .container-fluid').firstChild);
        }
    </script>
</body>
</html>
