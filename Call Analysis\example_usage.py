#!/usr/bin/env python3
"""
Example usage of RingCentral authentication with rc-credentials.json.
This script demonstrates how to get RingCentral access tokens.
"""

from rc_auth import (
    get_ringcentral_access_token,
    get_ringcentral_tokens,
    get_ringcentral_access_token_from_rc_credentials,
    refresh_ringcentral_access_token,
    refresh_ringcentral_tokens,
    get_ringcx_access_token_with_rc_token,
    extract_access_token
)

def example_ringcentral_auth():
    """Example of getting RingCentral access token."""

    print("Example: Getting RingCentral Access Token")
    print("=" * 50)

    try:
        # Method 1: Simple function to get just the access token
        print("\n1. Using convenience function...")
        rc_token = get_ringcentral_access_token()
        print(f"✓ RingCentral token: {rc_token[:50]}...")

        # Method 2: Get both access and refresh tokens
        print("\n2. Getting both access and refresh tokens...")
        access_token, refresh_token = get_ringcentral_tokens()
        print(f"✓ Access token: {access_token[:50]}...")
        print(f"✓ Refresh token: {refresh_token[:50]}...")

        # Method 3: Get full response
        print("\n3. Using full response function...")
        rc_response = get_ringcentral_access_token_from_rc_credentials()
        print(f"✓ Response keys: {list(rc_response.keys())}")
        print(f"✓ Token type: {rc_response.get('token_type')}")
        print(f"✓ Expires in: {rc_response.get('expires_in')} seconds")
        print(f"✓ Refresh token expires in: {rc_response.get('refresh_token_expires_in')} seconds")

        return access_token, refresh_token

    except Exception as e:
        print(f"✗ Error: {e}")
        return None, None

def example_token_refresh():
    """Example of refreshing tokens."""

    print("\n\nExample: Token Refresh")
    print("=" * 50)

    try:
        # First get initial tokens
        print("\n1. Getting initial tokens...")
        access_token, refresh_token = get_ringcentral_tokens()
        print(f"✓ Initial access token: {access_token[:50]}...")
        print(f"✓ Initial refresh token: {refresh_token[:50]}...")

        # Method 1: Refresh and get full response
        print("\n2. Refreshing tokens (full response)...")
        refresh_response = refresh_ringcentral_access_token(refresh_token)
        print(f"✓ Refresh response keys: {list(refresh_response.keys())}")
        print(f"✓ New token expires in: {refresh_response.get('expires_in')} seconds")

        # Method 2: Refresh and get both tokens directly
        print("\n3. Refreshing tokens (direct method)...")
        new_access_token, new_refresh_token = refresh_ringcentral_tokens(refresh_token)
        print(f"✓ New access token: {new_access_token[:50]}...")
        print(f"✓ New refresh token: {new_refresh_token[:50]}...")

        # Verify tokens are different
        if new_access_token != access_token:
            print("✓ Tokens successfully refreshed (new tokens are different)")
        else:
            print("⚠ Warning: New tokens are the same as old tokens")

        return new_access_token, new_refresh_token

    except Exception as e:
        print(f"✗ Token refresh failed: {e}")
        return None, None

def example_ringcx_auth():
    """Example of attempting RingCX authentication (may fail due to permissions)."""
    
    print("\n\nExample: Attempting RingCX Authentication")
    print("=" * 50)
    
    try:
        # First get RingCentral token
        print("1. Getting RingCentral token...")
        rc_token = get_ringcentral_access_token()
        print(f"✓ RC token obtained: {rc_token[:50]}...")
        
        # Try to get RingCX token
        print("\n2. Attempting RingCX authentication...")
        ringcx_response = get_ringcx_access_token_with_rc_token(rc_token)
        
        ringcx_token = extract_access_token(ringcx_response)
        print(f"✓ RingCX token: {ringcx_token[:50]}...")
        
        return ringcx_token
        
    except Exception as e:
        print(f"✗ RingCX authentication failed: {e}")
        print("\nNote: This may fail if your RingCentral application doesn't have")
        print("the required permissions (ReadAccounts) for RingCX access.")
        return None

def show_token_usage_example(token):
    """Show how to use the token for API calls."""
    
    if not token:
        return
        
    print("\n\nExample: Using Token for API Calls")
    print("=" * 50)
    
    print("Use the token in your API requests like this:")
    print()
    print("import requests")
    print()
    print("headers = {")
    print(f'    "Authorization": "Bearer {token[:30]}...",')
    print('    "Content-Type": "application/json"')
    print("}")
    print()
    print("# Example API call")
    print("response = requests.get(")
    print('    "https://platform.ringcentral.com/restapi/v1.0/account/~/extension/~",')
    print("    headers=headers")
    print(")")
    print()
    print("if response.status_code == 200:")
    print("    user_info = response.json()")
    print('    print(f"User: {user_info.get(\'name\')}")')

def main():
    """Main function to run examples."""

    print("RingCentral Authentication Examples")
    print("Using rc-credentials.json file")
    print("=" * 60)

    # Example 1: RingCentral authentication (should work)
    access_token, refresh_token = example_ringcentral_auth()

    # Example 2: Token refresh (should work)
    new_access_token, new_refresh_token = example_token_refresh()

    # Example 3: RingCX authentication (may fail due to permissions)
    ringcx_token = example_ringcx_auth()

    # Example 4: Show how to use the token
    token_to_show = ringcx_token if ringcx_token else (new_access_token or access_token)
    show_token_usage_example(token_to_show)

    print("\n" + "=" * 60)
    print("Summary:")
    print(f"✓ RingCentral authentication: {'Success' if access_token else 'Failed'}")
    print(f"✓ Token refresh: {'Success' if new_access_token else 'Failed'}")
    print(f"{'✓' if ringcx_token else '✗'} RingCX authentication: {'Success' if ringcx_token else 'Failed (likely permissions)'}")

    if access_token:
        print("\nAvailable tokens:")
        print(f"- Access token: {(new_access_token or access_token)[:50]}...")
        print(f"- Refresh token: {(new_refresh_token or refresh_token)[:50]}...")
        if ringcx_token:
            print(f"- RingCX token: {ringcx_token[:50]}...")

        print("\nNote: RingCentral tokens can be used for RingCentral API calls.")
        if not ringcx_token:
            print("For RingCX access, ensure your application has ReadAccounts permission.")

if __name__ == "__main__":
    main()
