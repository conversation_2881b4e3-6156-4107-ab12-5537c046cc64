<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-controller"></i> My Pinball Machines</h1>
                    <div>
                        <% if (totalMachineCount >= machineLimit) { %>
                            <button class="btn btn-secondary" disabled title="Machine limit reached">
                                <i class="bi bi-plus-circle"></i> Add Machine (Limit Reached)
                            </button>
                        <% } else { %>
                            <a href="/<%= operator.username %>/admin/machines/new" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Add Machine
                            </a>
                        <% } %>
                        <small class="text-muted ms-2">
                            <%= totalMachineCount %>/<%= machineLimit %> machines
                            <% if (totalMachineCount !== activeMachineCount) { %>
                                (<%= activeMachineCount %> active)
                            <% } %>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <% if (typeof success !== 'undefined' || typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <% if (typeof success !== 'undefined') { %>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <%= success %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    <% if (typeof error !== 'undefined') { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } %>

        <!-- Machines Grid -->
        <div class="row">
            <div class="col-12">
                <% if (machines.length === 0) { %>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-controller display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">No Machines Added Yet</h4>
                            <p class="text-muted">Add your first pinball machine to start tracking issues and generating QR codes.</p>
                            <a href="/<%= operator.username %>/admin/machines/new" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Add Your First Machine
                            </a>
                            <p class="text-muted mt-3">
                                <small>You can add up to <%= machineLimit %> machines to your inventory.</small>
                            </p>
                        </div>
                    </div>
                <% } else { %>
                    <div class="row g-4">
                        <% machines.forEach(machine => { %>
                            <div class="col-md-6 col-lg-4">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><%= machine.name %></h6>
                                        <span class="badge bg-secondary"><%= machine.manufacturer %></span>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <small class="text-muted">Type</small>
                                                <div><%= machine.machine_type %></div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Year</small>
                                                <div><%= machine.date_of_manufacture || 'Unknown' %></div>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <small class="text-muted">Open Issues</small>
                                                <div>
                                                    <%
                                                    let badgeClass = 'bg-success'; // Green for 0 issues
                                                    if (machine.open_issues_count === 1) badgeClass = 'bg-warning'; // Yellow for 1 issue
                                                    else if (machine.open_issues_count >= 2) badgeClass = 'bg-danger'; // Red for 2+ issues
                                                    %>
                                                    <span class="badge <%= badgeClass %>">
                                                        <%= machine.open_issues_count %>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Status</small>
                                                <div>
                                                    <span class="badge bg-<%= machine.status === 'active' ? 'success' : 'secondary' %>">
                                                        <%= machine.status === 'archived' ? 'decommissioned' : machine.status %>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <% if (machine.location_notes) { %>
                                            <div class="mb-3">
                                                <small class="text-muted">Location</small>
                                                <div class="small"><%= machine.location_notes %></div>
                                            </div>
                                        <% } %>


                                    </div>
                                    <div class="card-footer">
                                        <div class="d-flex justify-content-between">
                                            <a href="/<%= operator.username %>/admin/machines/<%= machine.id %>" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye"></i> Details
                                            </a>
                                            <% if (machine.qr_code_path) { %>
                                                <a href="<%= machine.qr_code_path %>" 
                                                   download="qr-code-<%= machine.name.replace(/\s+/g, '-').toLowerCase() %>.png"
                                                   class="btn btn-outline-success btn-sm">
                                                    <i class="bi bi-download"></i> QR Code
                                                </a>
                                            <% } %>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } %>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
    <!-- Machines JS -->
    <script src="/static/js/machines.js"></script>
</body>
</html>
