# Own Ship Extractor

A standalone application for extracting and visualizing data from TimeZero OwnShipRecorder.tzdb SQLite databases. The application provides both a graphical user interface (GUI) and command-line interface (CLI) for automated operation with Windows Task Scheduler.

## Features

### Database Detection and Selection
- Automatically scans for OwnShipRecorder.tzdb files in standard TimeZero paths:
  - `C:\ProgramData\TimeZero\DATA\` (TZ NAV)
  - `C:\ProgramData\TimeZeroREC\DATA\` (TZ PRO) 
  - `C:\ProgramData\TimeZeroVTS\DATA\` (TZ CM)
- Supports rootpath.txt redirection when present
- Manual file selection with .tzdb file filtering
- Database validation and schema checking

### Data Extraction and Display
- Extracts comprehensive navigation and environmental data:
  - Position (Latitude/Longitude)
  - Course Over Ground and Heading
  - Speed Through Water and Speed Over Ground
  - Depth and Sea Surface Temperature
  - Wind data (True/Apparent direction, speed, angle)
  - Environmental data (Air Temperature, Pressure, Humidity)
  - Motion data (Roll, Pitch, Heave)

### Time Range Selection
- Preset options: Last day, Last 7 days, Last month
- Custom date range selection
- UTC or Local time display options

### Data Visualization
- Dual-line graphs with time on Y-axis
- Selectable parameters for both graph lines
- Auto-scaling X-axes based on data range
- Available parameters:
  - Speed Through Water/Over Ground
  - Depth, Sea Surface Temperature
  - True/Apparent Wind Speed
  - Air Temperature, Atmospheric Pressure
  - Humidity, Roll, Pitch, Heave

### Export Functionality
- CSV export with all extracted data
- User-defined filenames for manual export
- Automatic timestamped filenames for CLI mode
- Supports both UTC and Local time formats

## Installation

### Requirements
- Python 3.7 or higher
- Required packages (install with pip):
  ```bash
  pip install matplotlib pandas tkcalendar
  ```

### Files
- `own_ship_extractor.py` - Main GUI application
- `cli_interface.py` - Command-line interface
- `database_manager.py` - Database detection and validation
- `data_extractor.py` - Data extraction and conversion
- `extract_ownship_data.bat` - Batch file for Windows Task Scheduler

## Usage

### GUI Mode
Run without arguments to open the graphical interface:
```bash
python own_ship_extractor.py
```

#### GUI Workflow:
1. **Database Selection**: Choose from detected databases or select manually
2. **Date Range**: Select time period for data extraction
3. **Load Data**: Click to extract and display data
4. **View Data**: Browse extracted records in the Data List tab
5. **Visualize**: View graphs in the Graph tab with selectable parameters
6. **Export**: Save data as CSV file

### Command Line Mode
For automated operation and Windows Task Scheduler integration:

```bash
# Basic usage - extract last 24 hours to Documents folder
python cli_interface.py "C:\Path\To\OwnShipRecorder.tzdb"

# Custom parameters
python cli_interface.py "C:\Path\To\OwnShipRecorder.tzdb" --output "C:\Exports" --hours 48 --local

# Detect available databases
python cli_interface.py --detect

# Force GUI mode
python cli_interface.py --gui
```

#### CLI Parameters:
- `database_path` - Path to OwnShipRecorder.tzdb file (required for CLI mode)
- `--output, -o` - Output folder for CSV file (default: Documents folder)
- `--hours, -t` - Time frame in hours from last entry (default: 24)
- `--local, -l` - Use local time instead of UTC
- `--gui, -g` - Force open GUI interface
- `--detect, -d` - Detect available databases and exit

### Windows Task Scheduler Integration

1. **Using the Batch File**:
   - Edit `extract_ownship_data.bat` to set your preferred parameters
   - Create a new task in Windows Task Scheduler
   - Set the action to run the batch file
   - Configure schedule as needed

2. **Direct Python Command**:
   - Program: `python.exe`
   - Arguments: `cli_interface.py --output "C:\YourOutputFolder" --hours 24`
   - Start in: Path to the application folder

## Data Format

### Database Schema
The application expects TimeZero OwnShipRecorder databases with the following structure:
- **Data table**: Contains timestamped navigation and sensor data
- **DataAvailability table**: Contains data availability information
- **ArchiverMetadata table**: Contains database metadata

### Data Conversion
- **Timestamps**: Converted from TimeZero format (milliseconds since 2000-01-01) to standard datetime
- **Coordinates**: Converted from Mercator projection (centimeters) to WGS84 decimal degrees using proper geodetic formulas
- **Measurements**: Scaled from integer format (0.01 precision) to floating-point values
- **Units**: 
  - Coordinates: Decimal degrees
  - Speeds: Knots
  - Depths/Distances: Meters
  - Temperatures: Celsius
  - Pressure: hPa
  - Angles: Degrees
  - Humidity: Percentage

### CSV Output Format
Exported CSV files contain the following columns:
- Date & Time (YYYY-MM-DD HH:MM:SS)
- Latitude, Longitude
- Course Over Ground, Heading (degrees)
- Speed Through Water, Speed Over Ground (knots)
- Depth (meters)
- Sea Surface Temperature, Air Temperature (Celsius)
- True Wind Direction, True Wind Speed
- Apparent Wind Angle, Apparent Wind Speed
- Atmospheric Pressure (hPa)
- Humidity (%)
- Roll, Pitch (degrees)
- Heave (meters)

## Troubleshooting

### Common Issues

1. **"No databases found"**
   - Check that TimeZero software is installed
   - Verify OwnShipRecorder.tzdb files exist in standard locations
   - Use manual file selection if databases are in custom locations

2. **"Database contains no data"**
   - Ensure the OwnShip Recorder has been running and collecting data
   - Check the date range - data might be outside the selected timeframe

3. **"Invalid database"**
   - Verify the file is a valid .tzdb file
   - Check that the database isn't corrupted
   - Ensure the database has the expected schema

4. **GUI won't start**
   - Verify all required packages are installed
   - Check Python version compatibility
   - Ensure tkinter is available (usually included with Python)

### Error Messages
- Check console output for detailed error messages
- Database validation errors provide specific information about missing tables/columns
- File permission errors may indicate the database is in use by another application

## Technical Details

### Architecture
- **Modular design** with separate components for database management, data extraction, GUI, and CLI
- **Database validation** ensures compatibility with OwnShip Recorder format
- **Flexible time handling** supports both UTC and local time zones
- **Scalable data processing** handles large datasets efficiently

### Performance
- Optimized for large datasets (tested with 90,000+ records)
- Efficient SQLite queries with time-based filtering
- Memory-conscious data processing for visualization

### Compatibility
- **TimeZero Products**: TZ Navigator, TZ Professional, TZ Coastal Monitoring
- **Windows**: Designed for Windows environments with Task Scheduler support
- **Python**: Compatible with Python 3.7+

## License

This software is provided as-is for use with TimeZero navigation systems. Please ensure compliance with your TimeZero software license when using this tool.
