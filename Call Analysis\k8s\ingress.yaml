apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: call-analysis-ingress
  labels:
    app: call-analysis
  annotations:
    # Common ingress annotations
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # File upload size limit (for potential CSV uploads)
    nginx.ingress.kubernetes.io/proxy-body-size: "16m"
    
    # Timeout settings for long-running analysis requests
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    
    # Optional: Enable cert-manager for automatic SSL certificates
    # cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
    # Optional: Basic authentication
    # nginx.ingress.kubernetes.io/auth-type: basic
    # nginx.ingress.kubernetes.io/auth-secret: call-analysis-auth
    # nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required - Call Analysis'
spec:
  # Uncomment and configure for HTTPS
  # tls:
  # - hosts:
  #   - call-analysis.yourdomain.com
  #   secretName: call-analysis-tls
  
  rules:
  - host: call-analysis.yourdomain.com  # Replace with your actual domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: call-analysis-service
            port:
              number: 80

---
# Optional: Basic authentication secret
# Create with: htpasswd -c auth username
# kubectl create secret generic call-analysis-auth --from-file=auth
apiVersion: v1
kind: Secret
metadata:
  name: call-analysis-auth
  labels:
    app: call-analysis
type: Opaque
data:
  # Replace with your htpasswd generated auth file (base64 encoded)
  # auth: ""
