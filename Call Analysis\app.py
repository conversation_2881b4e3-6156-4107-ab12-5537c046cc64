#!/usr/bin/env python3
"""
Call Analysis Web Application
Flask-based web interface for the Call Analysis system.
"""

import os
import json
from datetime import datetime
from flask import Flask, request, jsonify, render_template, send_file
from flask_cors import CORS
import threading
import tempfile

# Import our existing modules
from phone import (
    analyze_audio, extract_call_data_from_csv_string,
    save_to_database, generate_pdf_report, view_database_records,
    init_database, process_calls_with_progress
)

# Setup API key files from environment variables if running in container
def setup_api_keys():
    """Setup API key files from environment variables for containerized deployment."""

    # AssemblyAI API Key
    assemblyai_key = os.environ.get('ASSEMBLYAI_API_KEY')
    if assemblyai_key and not os.path.exists('Assembly_KEY.txt'):
        with open('Assembly_KEY.txt', 'w') as f:
            f.write(assemblyai_key)
        print("✓ Created Assembly_KEY.txt from environment variable")

    # OpenAI API Key
    openai_key = os.environ.get('OPENAI_API_KEY')
    if openai_key and not os.path.exists('OpenAI_KEY.txt'):
        with open('OpenAI_KEY.txt', 'w') as f:
            f.write(openai_key)
        print("✓ Created OpenAI_KEY.txt from environment variable")

    # RingCentral Credentials
    rc_credentials = os.environ.get('RC_CREDENTIALS_JSON')
    if rc_credentials and not os.path.exists('rc-credentials.json'):
        try:
            # If it's base64 encoded, decode it
            import base64
            try:
                decoded = base64.b64decode(rc_credentials).decode('utf-8')
                # Validate it's JSON
                json.loads(decoded)
                rc_credentials = decoded
            except:
                # If decoding fails, assume it's already plain text JSON
                pass

            with open('rc-credentials.json', 'w') as f:
                f.write(rc_credentials)
            print("✓ Created rc-credentials.json from environment variable")
        except Exception as e:
            print(f"⚠ Warning: Failed to create rc-credentials.json: {e}")

# Setup API keys on import
setup_api_keys()

try:
    from getReport import get_yesterday_call_report
    GETREPORT_AVAILABLE = True
except ImportError:
    GETREPORT_AVAILABLE = False

app = Flask(__name__)
CORS(app)

# Configure Flask
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Global variables for progress tracking
progress_data = {
    'current': 0,
    'total': 0,
    'status': 'idle',
    'message': '',
    'results': []
}

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('index.html')

@app.route('/health')
def health_check():
    """Health check endpoint for container orchestration."""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'getreport_available': GETREPORT_AVAILABLE
    })

@app.route('/api/config')
def get_config():
    """Get application configuration."""
    return jsonify({
        'getreport_available': GETREPORT_AVAILABLE,
        'database_initialized': os.path.exists('call_analysis.db')
    })

@app.route('/api/calls', methods=['GET'])
def get_calls():
    """Get call data from RingCX API."""
    try:
        days = request.args.get('days', 7, type=int)
        
        if not GETREPORT_AVAILABLE:
            return jsonify({'error': 'getReport module not available'}), 400
        
        # Get CSV data from RingCX
        csv_data = get_yesterday_call_report(save_file=False, days=days)
        
        if not csv_data:
            return jsonify({'error': 'No call data retrieved from RingCX'}), 404
        
        # Extract call data
        call_data = extract_call_data_from_csv_string(csv_data)
        
        # Format for frontend
        formatted_calls = []
        for call_record in call_data:
            if len(call_record) >= 6:
                agent_name, recording_url, ani, connected_dts, connected_duration, source_name = call_record
                formatted_calls.append({
                    'agent_name': agent_name,
                    'recording_url': recording_url,
                    'ani': ani,
                    'connected_dts': connected_dts,
                    'connected_duration': connected_duration,
                    'source_name': source_name
                })
        
        return jsonify({
            'calls': formatted_calls,
            'total': len(formatted_calls),
            'days': days
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['POST'])
def analyze_calls():
    """Analyze selected calls."""
    try:
        data = request.get_json()
        selected_calls = data.get('calls', [])
        
        if not selected_calls:
            return jsonify({'error': 'No calls selected'}), 400
        
        # Reset progress
        global progress_data
        progress_data = {
            'current': 0,
            'total': len(selected_calls),
            'status': 'processing',
            'message': 'Starting analysis...',
            'results': []
        }
        
        # Start analysis in background thread
        thread = threading.Thread(target=process_calls_background, args=(selected_calls,))
        thread.daemon = True
        thread.start()
        
        return jsonify({'message': 'Analysis started', 'total_calls': len(selected_calls)})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/progress')
def get_progress():
    """Get analysis progress."""
    return jsonify(progress_data)

@app.route('/api/database/records')
def get_database_records():
    """Get recent database records."""
    try:
        limit = request.args.get('limit', 10, type=int)
        records = view_database_records(limit)
        
        # Format records for JSON response
        formatted_records = []
        for record in records:
            formatted_records.append({
                'id': record[0],
                'date_time': record[1],
                'agent_name': record[2],
                'url_preview': record[3],
                'sentiment_score': record[4],
                'summary_preview': record[5],
                'chatgpt_preview': record[6],
                'source_name': record[7] if len(record) > 7 else None,
                'ani': record[8] if len(record) > 8 else None,
                'connected_dts': record[9] if len(record) > 9 else None,
                'connected_duration': record[10] if len(record) > 10 else None,
                'created_at': record[11] if len(record) > 11 else None
            })
        
        return jsonify({'records': formatted_records})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/reports/generate', methods=['POST'])
def generate_report():
    """Generate PDF report from analysis results."""
    try:
        # Get results from progress_data or database
        if not progress_data['results']:
            return jsonify({'error': 'No analysis results available'}), 400
        
        # Generate PDF report
        pdf_path = generate_pdf_report(progress_data['results'])
        
        return jsonify({
            'message': 'PDF report generated successfully',
            'filename': os.path.basename(pdf_path),
            'path': pdf_path
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/reports/download/<filename>')
def download_report(filename):
    """Download generated PDF report."""
    try:
        # Security check - only allow PDF files in current directory
        if not filename.endswith('.pdf') or '/' in filename or '\\' in filename:
            return jsonify({'error': 'Invalid filename'}), 400
        
        file_path = os.path.join(os.getcwd(), filename)
        
        if not os.path.exists(file_path):
            return jsonify({'error': 'File not found'}), 404
        
        return send_file(file_path, as_attachment=True, download_name=filename)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def process_calls_background(selected_calls):
    """Process calls in background thread."""
    global progress_data
    
    try:
        results = []
        call_data_dict = {}
        
        for i, call_record in enumerate(selected_calls):
            # Update progress
            progress_data['current'] = i
            progress_data['message'] = f"Processing call {i+1} of {len(selected_calls)}: {call_record.get('agent_name', 'Unknown')}"
            
            try:
                # Convert call_record dict to tuple format expected by analyze_audio
                agent_name = call_record['agent_name']
                recording_url = call_record['recording_url']
                ani = call_record.get('ani', '')
                connected_dts = call_record.get('connected_dts', '')
                connected_duration = call_record.get('connected_duration', '')
                source_name = call_record.get('source_name', '')
                
                # Store CSV data for database
                call_data_dict[recording_url] = (source_name, ani, connected_dts, connected_duration)
                
                # Analyze the call
                summary, sentiment_score, chatgpt_summary = analyze_audio(recording_url, agent_name)
                results.append((agent_name, recording_url, summary, sentiment_score, chatgpt_summary))
                
            except Exception as e:
                print(f"Error processing call for {call_record.get('agent_name', 'Unknown')}: {str(e)}")
        
        # Save to database
        if results:
            save_to_database(results, call_data_dict)
        
        # Update final progress
        progress_data['current'] = len(selected_calls)
        progress_data['status'] = 'completed'
        progress_data['message'] = f'Analysis completed! Processed {len(results)} calls successfully.'
        progress_data['results'] = results
        
    except Exception as e:
        progress_data['status'] = 'error'
        progress_data['message'] = f'Error during analysis: {str(e)}'

if __name__ == '__main__':
    # Initialize database
    init_database()
    
    # Run Flask app
    app.run(host='0.0.0.0', port=5000, debug=False)
