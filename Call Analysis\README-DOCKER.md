# Call Analysis - Containerized Deployment

This project has been containerized for easy deployment on cloud platforms. The original tkinter GUI has been replaced with a modern web interface.

## 🚀 Quick Start

### Prerequisites
1. **API Keys**: You need the following API keys:
   - AssemblyAI API key
   - OpenAI API key
   - RingCentral credentials (rc-credentials.json)

2. **Tools**: Install Docker and optionally kubectl for Kubernetes deployment.

### Setup API Keys
```bash
# Create API key files
echo "your-assemblyai-api-key" > Assembly_KEY.txt
echo "your-openai-api-key" > OpenAI_KEY.txt

# Copy your RingCentral credentials
cp your-existing-rc-credentials.json rc-credentials.json
```

### Deploy with <PERSON>er Compose (Recommended for local testing)
```bash
# Make deployment script executable
chmod +x deploy.sh

# Deploy the application
./deploy.sh docker

# Access the application
open http://localhost:5000
```

### Deploy to Kubernetes (For production)
```bash
# Deploy to your Kubernetes cluster
./deploy.sh k8s

# Access via port forwarding
kubectl port-forward service/call-analysis-service 5000:80 -n call-analysis
open http://localhost:5000
```

## 🌐 Web Interface Features

The new web interface provides:

- **Dashboard**: Modern, responsive web UI
- **Call Data Loading**: Fetch calls from RingCX API with date range selection
- **Call Selection**: Interactive table with checkboxes for selecting calls to analyze
- **Progress Tracking**: Real-time progress bar during analysis
- **Results Display**: View analysis results and sentiment scores
- **PDF Reports**: Generate and download PDF reports
- **Database View**: Browse historical analysis records

## 📁 Project Structure

```
├── app.py                 # Flask web application
├── phone.py              # Core analysis logic (unchanged)
├── getReport.py          # RingCX API integration (unchanged)
├── rc_auth.py            # Authentication (unchanged)
├── templates/
│   └── index.html        # Web interface
├── k8s/                  # Kubernetes manifests
│   ├── pod.yaml         # Pod configuration
│   ├── deployment.yaml  # Deployment configuration
│   ├── service.yaml     # Service configuration
│   ├── ingress.yaml     # Ingress configuration
│   ├── configmap.yaml   # Configuration
│   ├── secret.yaml      # Secrets template
│   └── pvc.yaml         # Persistent volume claims
├── Dockerfile           # Container image definition
├── docker-compose.yml   # Local deployment
├── deploy.sh           # Automated deployment script
└── DEPLOYMENT.md       # Detailed deployment guide
```

## 🔧 Configuration

### Environment Variables
- `FLASK_ENV`: Flask environment (production/development)
- `DATABASE_PATH`: SQLite database path
- `REPORTS_PATH`: PDF reports storage path
- `RINGCX_BASE_URL`: RingCX API base URL

### Storage
- **Database**: Persistent SQLite database
- **Reports**: Generated PDF reports
- **Logs**: Application and access logs

## 🔒 Security

- API keys stored as Kubernetes secrets
- Container runs as non-root user
- Resource limits configured
- Health checks implemented
- Optional ingress with TLS

## 📊 Monitoring

### Health Check
The application provides a health endpoint at `/health` that returns:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "getreport_available": true
}
```

### Logs
```bash
# Docker Compose
docker-compose logs -f call-analysis

# Kubernetes
kubectl logs -f deployment/call-analysis -n call-analysis
```

## 🛠 Troubleshooting

### Common Issues

1. **API Key Errors**
   - Verify API key files exist and contain valid keys
   - Check Kubernetes secrets: `kubectl get secrets -n call-analysis`

2. **Database Issues**
   - Check persistent volume status: `kubectl get pvc -n call-analysis`
   - Verify database file permissions

3. **Network Issues**
   - Test service connectivity: `kubectl port-forward service/call-analysis-service 5000:80`
   - Check ingress configuration and DNS

4. **Resource Issues**
   - Monitor resource usage: `kubectl top pods -n call-analysis`
   - Adjust resource limits in deployment.yaml

### Debug Commands
```bash
# Check pod status
kubectl get pods -n call-analysis

# View pod logs
kubectl logs -f pod-name -n call-analysis

# Execute into pod
kubectl exec -it pod-name -n call-analysis -- /bin/bash

# Check events
kubectl get events -n call-analysis
```

## 🔄 Updates and Maintenance

### Update Application
```bash
# Rebuild image
docker build -t call-analysis:latest .

# Restart deployment
kubectl rollout restart deployment/call-analysis -n call-analysis
```

### Backup Database
```bash
# Create backup
kubectl exec deployment/call-analysis -n call-analysis -- \
  sqlite3 /app/data/call_analysis.db ".backup /app/data/backup.db"

# Download backup
kubectl cp call-analysis/pod-name:/app/data/backup.db ./backup.db
```

## 🧹 Cleanup

```bash
# Clean up all deployments
./deploy.sh clean
```

## 📞 Support

For deployment issues:
1. Check the detailed [DEPLOYMENT.md](DEPLOYMENT.md) guide
2. Review application logs
3. Verify API key configuration
4. Test network connectivity

## 🔗 Related Files

- [DEPLOYMENT.md](DEPLOYMENT.md) - Comprehensive deployment guide
- [phone.py](phone.py) - Original analysis logic
- [getReport.py](getReport.py) - RingCX API integration
- [requirements.txt](requirements.txt) - Python dependencies
