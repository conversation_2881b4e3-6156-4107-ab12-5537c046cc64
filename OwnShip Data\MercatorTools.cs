﻿using System;

namespace SampleCode
{
	class MercatorTools
	{
		/// <summary>Converts a value representing a mercator Y coordinate expressed in centimeter to a WGS84 latitude expressed in radian.</summary>
		public static double MercatorYToWgs84Latitude(double yCentimeter)
		{
			double y = yCentimeter / 100.0; // convert from centimeter to meter
			double phi = (Math.PI / 2.0) - (2.0 * Math.Atan(-y / Wgs84EquatorialRadiusInMeter));

			int i = 0;
			double dPhi = 0;
			do
			{
				double con = Wgs84Eccentricity * Math.Sin(phi);
				double oldPhi = phi;
				phi = (Math.PI / 2.0) - (2.0 * Math.Atan(Math.Exp(-y / Wgs84EquatorialRadiusInMeter) * Math.Pow((1.0 - con) / (1.0 + con), (Wgs84Eccentricity / 2.0))));
				dPhi = Math.Abs(oldPhi - phi);
			}
			while ((++i < 15) && (dPhi > ONE_CENTIMETER_RAD));

			double latRadian = phi;
			return latRadian;
		}

		/// <summary>Converts a value representing a mercator X coordinate expressed in centimeter to a WGS84 longitude expressed in radian.</summary>
		public static double MercatorXToWgs84Longitude(double xCentimeter)
		{
			double x = xCentimeter / 100.0; // convert from centimeter to meter
			return x / Wgs84EquatorialRadiusInMeter;
		}

		/// <summary>Converts a value representing a WGS84 latitude expressed in radian to a mercator Y coordinate expressed in centimeter.</summary>
		public static double Wgs84LatitudeToMercatorY(double latitudeRadian)
		{
			double psi = (Math.PI / 2.0) - latitudeRadian;
			double factor = Wgs84Eccentricity * Math.Cos(psi);
			return 100.0 * (-Wgs84EquatorialRadiusInMeter * Math.Log(Math.Abs(Math.Tan(psi / 2)) * Math.Pow(((1 + factor) / (1 - factor)), Wgs84Eccentricity / 2)));
		}

		/// <summary>Converts a value representing a WGS84 longitude expressed in radian to a mercator X coordinate expressed in centimeter.</summary>
		public static double Wgs84LongitudeToMercatorX(double longitudeRadian)
		{
			return 100.0 * Wgs84EquatorialRadiusInMeter * longitudeRadian;
		}

		private const double Wgs84EquatorialRadiusInMeter = 6378137.0;
		private static readonly double Wgs84Eccentricity = Math.Sqrt(Wgs84SquareEccentricity); // Note: cannot be declared as constant; C# compiler cannot evaluate a Sqrt 
		private const double ONE_CENTIMETER_RAD = 1.570670673141045335604473333773e-9;
		private const double Wgs84FlatteningRatio = 298.257223563;
		private const double Wgs84Flattening = 1.0 / Wgs84FlatteningRatio; // 0.0033528106643315511638122819299254
		private const double Wgs84SquareEccentricity = (2.0 * Wgs84Flattening) - (Wgs84Flattening * Wgs84Flattening); // e²=0.006694379989312246950173071074  / e=0.081819190837555017647294378542738
	}
}
