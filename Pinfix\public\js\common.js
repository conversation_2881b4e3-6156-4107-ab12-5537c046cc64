// Common JavaScript functionality across all pages
document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss alerts after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert-danger, .alert-success, .alert-warning, .alert-info');
        alerts.forEach(alert => {
            // Check if alert has dismiss button (some alerts might not be dismissible)
            if (alert.querySelector('.btn-close')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
});
