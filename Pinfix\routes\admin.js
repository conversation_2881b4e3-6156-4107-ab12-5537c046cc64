const express = require('express');
const bcrypt = require('bcrypt');
const { requireSuperAdmin } = require('../middleware/auth');
const db = require('../models/database');
const { getRecentActivities, logActivity } = require('../utils/activity-logger');
const QRCode = require('qrcode');
const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const router = express.Router();

// Apply super admin authentication to all routes
router.use(requireSuperAdmin);

// Super Admin Dashboard
router.get('/', async (req, res) => {
  try {
    // Get statistics
    const operatorCount = await db.get('SELECT COUNT(*) as count FROM operators');
    const activeOperatorCount = await db.get('SELECT COUNT(*) as count FROM operators WHERE status = "active"');
    const issueCount = await db.get('SELECT COUNT(*) as count FROM issues');
    const machineCount = await db.get('SELECT COUNT(*) as count FROM global_pinball_machines');

    // Get recent activities
    const recentActivities = await getRecentActivities(15);

    res.render('admin/dashboard', {
      title: 'Super Admin Dashboard',
      session: req.session,
      stats: {
        operators: operatorCount.count,
        activeOperators: activeOperatorCount.count,
        issues: issueCount.count,
        machines: machineCount.count
      },
      activities: recentActivities
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.render('admin/dashboard', {
      title: 'Super Admin Dashboard',
      session: req.session,
      stats: { operators: 0, activeOperators: 0, issues: 0, machines: 0 },
      activities: []
    });
  }
});

// Operator Management Routes
router.get('/operators', async (req, res) => {
  try {
    const operators = await db.all(`
      SELECT o.*,
             COUNT(DISTINCT CASE WHEN pm.status = 'active' THEN pm.id END) as machine_count,
             COUNT(DISTINCT i.id) as issue_count
      FROM operators o
      LEFT JOIN pinball_machines pm ON o.id = pm.operator_id
      LEFT JOIN issues i ON o.id = i.operator_id
      GROUP BY o.id
      ORDER BY o.created_at DESC
    `);

    res.render('admin/operators', {
      title: 'Manage Operators',
      session: req.session,
      operators
    });
  } catch (error) {
    console.error('Operators list error:', error);
    res.render('admin/operators', {
      title: 'Manage Operators',
      session: req.session,
      operators: [],
      error: 'Failed to load operators'
    });
  }
});

// Create new operator form
router.get('/operators/new', (req, res) => {
  res.render('admin/operator-form', {
    title: 'Create New Operator',
    session: req.session,
    operator: null,
    isEdit: false
  });
});

// Create new operator
router.post('/operators', async (req, res) => {
  try {
    const { name, email, phone, address, username, password, status, email_notifications_enabled, machine_limit } = req.body;

    // Validate required fields
    if (!name || !email || !username || !password) {
      return res.render('admin/operator-form', {
        title: 'Create New Operator',
        session: req.session,
        operator: req.body,
        isEdit: false,
        error: 'Name, email, username, and password are required'
      });
    }

    // Check if username or email already exists
    const existingOperator = await db.get(
      'SELECT id FROM operators WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingOperator) {
      return res.render('admin/operator-form', {
        title: 'Create New Operator',
        session: req.session,
        operator: req.body,
        isEdit: false,
        error: 'Username or email already exists'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Validate machine limit
    const machineLimit = parseInt(machine_limit) || 10;
    if (machineLimit < 1 || machineLimit > 1000) {
      return res.render('admin/operator-form', {
        title: 'Add New Operator',
        session: req.session,
        operator: req.body,
        isEdit: false,
        error: 'Machine limit must be between 1 and 1000'
      });
    }

    // Create operator
    const result = await db.run(`
      INSERT INTO operators (name, email, phone, address, username, password_hash, status, email_notifications_enabled, machine_limit, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `, [name, email, phone || null, address || null, username, hashedPassword, status || 'active', email_notifications_enabled ? 1 : 0, machineLimit]);

    // Log activity
    await logActivity('operator_created', {
      operatorId: result.id,
      actorType: 'super_admin',
      actorName: 'Super Admin',
      description: `New operator "${name}" created with username "${username}"`
    });

    res.redirect('/admin/operators?success=Operator created successfully');
  } catch (error) {
    console.error('Create operator error:', error);
    res.render('admin/operator-form', {
      title: 'Create New Operator',
      session: req.session,
      operator: req.body,
      isEdit: false,
      error: 'Failed to create operator'
    });
  }
});

// Edit operator form
router.get('/operators/:id/edit', async (req, res) => {
  try {
    const operator = await db.get('SELECT * FROM operators WHERE id = ?', [req.params.id]);

    if (!operator) {
      return res.redirect('/admin/operators?error=Operator not found');
    }

    res.render('admin/operator-form', {
      title: 'Edit Operator',
      session: req.session,
      operator,
      isEdit: true
    });
  } catch (error) {
    console.error('Edit operator error:', error);
    res.redirect('/admin/operators?error=Failed to load operator');
  }
});

// Regenerate all QR codes for a specific operator (Super Admin only)
router.post('/operators/:id/regenerate-all-qr', async (req, res) => {
  try {
    const operatorId = req.params.id;

    // Get operator details
    const operator = await db.get('SELECT * FROM operators WHERE id = ?', [operatorId]);
    if (!operator) {
      return res.redirect('/admin/operators?error=Operator not found');
    }

    // Get all machines for this operator
    const machines = await db.all(
      'SELECT id FROM pinball_machines WHERE operator_id = ?',
      [operatorId]
    );

    let regeneratedCount = 0;

    for (const machine of machines) {
      try {
        const machineId = machine.id;
        const qrCodeUrl = `${process.env.BASE_URL || 'http://localhost:3000'}/${operator.username}/machine/${machineId}`;

        // Create QR code directory if it doesn't exist
        const qrDir = path.join(__dirname, '../uploads/qr-codes');
        if (!fs.existsSync(qrDir)) {
          fs.mkdirSync(qrDir, { recursive: true });
        }

        // Generate QR code file
        const qrFileName = `qr-${operatorId}-${machineId}.png`;
        const qrFilePath = path.join(qrDir, qrFileName);

        // First generate QR code to a buffer
        const qrBuffer = await QRCode.toBuffer(qrCodeUrl, {
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });

        // Get machine name for the label
        const machineInfo = await db.get(`
          SELECT gpm.name
          FROM pinball_machines pm
          JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
          WHERE pm.id = ?
        `, [machineId]);

        // Create text image with machine name
        const topTextHeight = 60;
        const bottomTextHeight = 40;
        const qrWidth = 300;
        const totalHeight = topTextHeight + 300 + bottomTextHeight + 30; // top text + QR + bottom text + padding

        // Create a white background with text
        const textSvg = `
          <svg width="${qrWidth}" height="${totalHeight}">
            <rect width="${qrWidth}" height="${totalHeight}" fill="white"/>
            <text x="${qrWidth/2}" y="25" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="black">
              Scan me to
            </text>
            <text x="${qrWidth/2}" y="50" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="black">
              report an issue
            </text>
            <text x="${qrWidth/2}" y="${topTextHeight + 300 + 25}" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="black">
              ${machineInfo ? machineInfo.name : 'Pinball Machine'}
            </text>
          </svg>
        `;

        // Convert SVG to buffer
        const textBuffer = Buffer.from(textSvg);

        // Composite the text and QR code
        await sharp(textBuffer)
          .composite([
            {
              input: qrBuffer,
              top: topTextHeight + 10,
              left: 0
            }
          ])
          .png()
          .toFile(qrFilePath);

        // Update machine with QR code path
        await db.run(
          'UPDATE pinball_machines SET qr_code_path = ? WHERE id = ?',
          [`/uploads/qr-codes/${qrFileName}`, machineId]
        );

        regeneratedCount++;
      } catch (error) {
        console.error(`Failed to regenerate QR code for machine ${machine.id}:`, error);
      }
    }

    res.redirect(`/admin/operators/${operatorId}/edit?success=${regeneratedCount} QR code(s) regenerated successfully for ${operator.name}`);
  } catch (error) {
    console.error('Regenerate all QR codes error:', error);
    res.redirect(`/admin/operators?error=Failed to regenerate QR codes`);
  }
});

// Update operator
router.post('/operators/:id', async (req, res) => {
  try {
    const { name, email, phone, address, username, password, status, email_notifications_enabled, machine_limit } = req.body;
    const operatorId = req.params.id;

    // Validate required fields
    if (!name || !email || !username) {
      const operator = await db.get('SELECT * FROM operators WHERE id = ?', [operatorId]);
      return res.render('admin/operator-form', {
        title: 'Edit Operator',
        session: req.session,
        operator: { ...operator, ...req.body },
        isEdit: true,
        error: 'Name, email, and username are required'
      });
    }

    // Check if username or email already exists (excluding current operator)
    const existingOperator = await db.get(
      'SELECT id FROM operators WHERE (username = ? OR email = ?) AND id != ?',
      [username, email, operatorId]
    );

    if (existingOperator) {
      const operator = await db.get('SELECT * FROM operators WHERE id = ?', [operatorId]);
      return res.render('admin/operator-form', {
        title: 'Edit Operator',
        session: req.session,
        operator: { ...operator, ...req.body },
        isEdit: true,
        error: 'Username or email already exists'
      });
    }

    // Get current operator data to check for status changes
    const currentOperator = await db.get('SELECT * FROM operators WHERE id = ?', [operatorId]);
    const newStatus = status || 'active';

    // Validate machine limit
    const machineLimit = parseInt(machine_limit) || 10;
    if (machineLimit < 1 || machineLimit > 1000) {
      const operator = await db.get('SELECT * FROM operators WHERE id = ?', [operatorId]);
      return res.render('admin/operator-form', {
        title: 'Edit Operator',
        session: req.session,
        operator: { ...operator, ...req.body },
        isEdit: true,
        error: 'Machine limit must be between 1 and 1000'
      });
    }

    // Update operator (with or without password)
    if (password && password.trim()) {
      const hashedPassword = await bcrypt.hash(password, 10);
      await db.run(`
        UPDATE operators
        SET name = ?, email = ?, phone = ?, address = ?, username = ?, password_hash = ?, status = ?, email_notifications_enabled = ?, machine_limit = ?
        WHERE id = ?
      `, [name, email, phone || null, address || null, username, hashedPassword, newStatus, email_notifications_enabled ? 1 : 0, machineLimit, operatorId]);
    } else {
      await db.run(`
        UPDATE operators
        SET name = ?, email = ?, phone = ?, address = ?, username = ?, status = ?, email_notifications_enabled = ?, machine_limit = ?
        WHERE id = ?
      `, [name, email, phone || null, address || null, username, newStatus, email_notifications_enabled ? 1 : 0, machineLimit, operatorId]);
    }

    // Log activity if status changed
    if (currentOperator.status !== newStatus) {
      await logActivity('operator_status_changed', {
        operatorId: operatorId,
        actorType: 'super_admin',
        actorName: 'Super Admin',
        description: `Operator "${name}" status changed from ${currentOperator.status} to ${newStatus}`
      });
    }

    res.redirect('/admin/operators?success=Operator updated successfully');
  } catch (error) {
    console.error('Update operator error:', error);
    const operator = await db.get('SELECT * FROM operators WHERE id = ?', [req.params.id]);
    res.render('admin/operator-form', {
      title: 'Edit Operator',
      session: req.session,
      operator: { ...operator, ...req.body },
      isEdit: true,
      error: 'Failed to update operator'
    });
  }
});

// Toggle operator status
router.post('/operators/:id/toggle-status', async (req, res) => {
  try {
    const operator = await db.get('SELECT * FROM operators WHERE id = ?', [req.params.id]);

    if (!operator) {
      return res.redirect('/admin/operators?error=Operator not found');
    }

    const newStatus = operator.status === 'active' ? 'inactive' : 'active';
    await db.run('UPDATE operators SET status = ? WHERE id = ?', [newStatus, req.params.id]);

    // Log activity
    await logActivity('operator_status_changed', {
      operatorId: operator.id,
      actorType: 'super_admin',
      actorName: 'Super Admin',
      description: `Operator "${operator.name}" status changed from ${operator.status} to ${newStatus}`
    });

    res.redirect('/admin/operators?success=Operator status updated');
  } catch (error) {
    console.error('Toggle status error:', error);
    res.redirect('/admin/operators?error=Failed to update operator status');
  }
});

// Global Pinball Machines Management
router.get('/machines', async (req, res) => {
  try {
    const machines = await db.all(`
      SELECT gpm.*,
             COUNT(pm.id) as operator_count
      FROM global_pinball_machines gpm
      LEFT JOIN pinball_machines pm ON gpm.id = pm.global_machine_id
      GROUP BY gpm.id
      ORDER BY gpm.manufacturer, gpm.name
    `);

    res.render('admin/machines', {
      title: 'Global Pinball Machines',
      session: req.session,
      machines
    });
  } catch (error) {
    console.error('Machines list error:', error);
    res.render('admin/machines', {
      title: 'Global Pinball Machines',
      session: req.session,
      machines: [],
      error: 'Failed to load machines'
    });
  }
});

// Create new machine form
router.get('/machines/new', (req, res) => {
  res.render('admin/machine-form', {
    title: 'Add New Pinball Machine',
    session: req.session,
    machine: null,
    isEdit: false
  });
});

// Create new machine
router.post('/machines', async (req, res) => {
  try {
    const { name, manufacturer, date_of_manufacture, machine_type } = req.body;

    // Validate required fields
    if (!name || !manufacturer || !machine_type) {
      return res.render('admin/machine-form', {
        title: 'Add New Pinball Machine',
        session: req.session,
        machine: req.body,
        isEdit: false,
        error: 'Name, manufacturer, and machine type are required'
      });
    }

    // Check if machine already exists
    const existingMachine = await db.get(
      'SELECT id FROM global_pinball_machines WHERE name = ? AND manufacturer = ?',
      [name, manufacturer]
    );

    if (existingMachine) {
      return res.render('admin/machine-form', {
        title: 'Add New Pinball Machine',
        session: req.session,
        machine: req.body,
        isEdit: false,
        error: 'A machine with this name and manufacturer already exists'
      });
    }

    // Create machine
    await db.run(`
      INSERT INTO global_pinball_machines (name, manufacturer, date_of_manufacture, machine_type)
      VALUES (?, ?, ?, ?)
    `, [name, manufacturer, date_of_manufacture || null, machine_type]);

    res.redirect('/admin/machines?success=Machine added successfully');
  } catch (error) {
    console.error('Create machine error:', error);
    res.render('admin/machine-form', {
      title: 'Add New Pinball Machine',
      session: req.session,
      machine: req.body,
      isEdit: false,
      error: 'Failed to create machine'
    });
  }
});

// Edit machine form
router.get('/machines/:id/edit', async (req, res) => {
  try {
    const machine = await db.get('SELECT * FROM global_pinball_machines WHERE id = ?', [req.params.id]);

    if (!machine) {
      return res.redirect('/admin/machines?error=Machine not found');
    }

    res.render('admin/machine-form', {
      title: 'Edit Pinball Machine',
      session: req.session,
      machine,
      isEdit: true
    });
  } catch (error) {
    console.error('Edit machine error:', error);
    res.redirect('/admin/machines?error=Failed to load machine');
  }
});

// Update machine
router.post('/machines/:id', async (req, res) => {
  try {
    const { name, manufacturer, date_of_manufacture, machine_type } = req.body;
    const machineId = req.params.id;

    // Validate required fields
    if (!name || !manufacturer || !machine_type) {
      const machine = await db.get('SELECT * FROM global_pinball_machines WHERE id = ?', [machineId]);
      return res.render('admin/machine-form', {
        title: 'Edit Pinball Machine',
        session: req.session,
        machine: { ...machine, ...req.body },
        isEdit: true,
        error: 'Name, manufacturer, and machine type are required'
      });
    }

    // Check if machine name/manufacturer combination already exists (excluding current machine)
    const existingMachine = await db.get(
      'SELECT id FROM global_pinball_machines WHERE name = ? AND manufacturer = ? AND id != ?',
      [name, manufacturer, machineId]
    );

    if (existingMachine) {
      const machine = await db.get('SELECT * FROM global_pinball_machines WHERE id = ?', [machineId]);
      return res.render('admin/machine-form', {
        title: 'Edit Pinball Machine',
        session: req.session,
        machine: { ...machine, ...req.body },
        isEdit: true,
        error: 'A machine with this name and manufacturer already exists'
      });
    }

    // Update machine
    await db.run(`
      UPDATE global_pinball_machines
      SET name = ?, manufacturer = ?, date_of_manufacture = ?, machine_type = ?
      WHERE id = ?
    `, [name, manufacturer, date_of_manufacture || null, machine_type, machineId]);

    res.redirect('/admin/machines?success=Machine updated successfully');
  } catch (error) {
    console.error('Update machine error:', error);
    const machine = await db.get('SELECT * FROM global_pinball_machines WHERE id = ?', [req.params.id]);
    res.render('admin/machine-form', {
      title: 'Edit Pinball Machine',
      session: req.session,
      machine: { ...machine, ...req.body },
      isEdit: true,
      error: 'Failed to update machine'
    });
  }
});

// System Reports
router.get('/reports', requireSuperAdmin, async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');

    // Basic Statistics
    const totalIssues = await db.get('SELECT COUNT(*) as count FROM issues');
    const totalPictures = await db.get('SELECT COUNT(*) as count FROM issues WHERE user_picture_path IS NOT NULL');
    const totalOperators = await db.get('SELECT COUNT(*) as count FROM operators');
    const totalMachines = await db.get('SELECT COUNT(*) as count FROM pinball_machines');
    const totalTechs = await db.get('SELECT COUNT(*) as count FROM techs');

    // Database size
    let dbSize = 0;
    try {
      const dbPath = path.join(__dirname, '../data/pinfix.db');
      const stats = fs.statSync(dbPath);
      dbSize = (stats.size / (1024 * 1024)).toFixed(2); // Size in MB
    } catch (error) {
      console.error('Error getting database size:', error);
    }

    // Issues by Machine Type
    const issuesByMachineType = await db.all(`
      SELECT
        gpm.machine_type,
        COUNT(i.id) as issue_count
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      GROUP BY gpm.machine_type
      ORDER BY issue_count DESC
    `);

    // Issues created over time (last 30 days)
    const issuesOverTime = await db.all(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as count
      FROM issues
      WHERE created_at >= datetime('now', '-30 days')
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `);

    // Weekly issues (last 12 weeks)
    const weeklyIssues = await db.all(`
      SELECT
        strftime('%Y-W%W', created_at) as week,
        COUNT(*) as count
      FROM issues
      WHERE created_at >= datetime('now', '-84 days')
      GROUP BY strftime('%Y-W%W', created_at)
      ORDER BY week ASC
    `);

    // Monthly issues (last 12 months)
    const monthlyIssues = await db.all(`
      SELECT
        strftime('%Y-%m', created_at) as month,
        COUNT(*) as count
      FROM issues
      WHERE created_at >= datetime('now', '-365 days')
      GROUP BY strftime('%Y-%m', created_at)
      ORDER BY month ASC
    `);

    // Average resolution time
    const avgResolutionTime = await db.get(`
      SELECT
        AVG(JULIANDAY(resolved_at) - JULIANDAY(created_at)) * 24 as avg_hours
      FROM issues
      WHERE resolved_at IS NOT NULL
    `);

    // System health checks
    let systemStatus = 'Live';
    let systemStatusColor = 'success';
    let systemStatusIcon = 'bi-activity';

    try {
      // Check recent activity (issues created in last hour)
      const recentActivity = await db.get(`
        SELECT COUNT(*) as count
        FROM issues
        WHERE created_at >= datetime('now', '-1 hour')
      `);

      // Check database responsiveness
      const dbCheck = await db.get('SELECT 1 as test');

      // Check for any recent errors (you could expand this)
      const errorCheck = await db.get(`
        SELECT COUNT(*) as count
        FROM activity_log
        WHERE created_at >= datetime('now', '-5 minutes')
      `);

      // Determine system status based on checks
      if (!dbCheck || dbCheck.test !== 1) {
        systemStatus = 'Database Error';
        systemStatusColor = 'danger';
        systemStatusIcon = 'bi-exclamation-triangle';
      } else if (recentActivity.count === 0 && errorCheck.count === 0) {
        systemStatus = 'Idle';
        systemStatusColor = 'warning';
        systemStatusIcon = 'bi-pause-circle';
      } else if (recentActivity.count > 10) {
        systemStatus = 'High Activity';
        systemStatusColor = 'info';
        systemStatusIcon = 'bi-lightning';
      } else {
        systemStatus = 'Active';
        systemStatusColor = 'success';
        systemStatusIcon = 'bi-activity';
      }
    } catch (error) {
      console.error('System status check error:', error);
      systemStatus = 'Error';
      systemStatusColor = 'danger';
      systemStatusIcon = 'bi-x-circle';
    }

    // Top 5 operators by issue volume
    const topOperatorsByIssues = await db.all(`
      SELECT
        o.name,
        o.username,
        COUNT(i.id) as issue_count
      FROM operators o
      LEFT JOIN issues i ON o.id = i.operator_id
      GROUP BY o.id, o.name, o.username
      ORDER BY issue_count DESC
      LIMIT 5
    `);

    // Issue status distribution
    const issueStatusDistribution = await db.all(`
      SELECT
        status,
        COUNT(*) as count
      FROM issues
      GROUP BY status
      ORDER BY count DESC
    `);

    // Issue priority distribution
    const issuePriorityDistribution = await db.all(`
      SELECT
        priority,
        COUNT(*) as count
      FROM issues
      GROUP BY priority
      ORDER BY
        CASE priority
          WHEN 'High' THEN 1
          WHEN 'Medium' THEN 2
          WHEN 'Low' THEN 3
        END
    `);

    // Most common issue types
    const topIssueTypes = await db.all(`
      SELECT
        issue_type,
        COUNT(*) as count
      FROM issues
      GROUP BY issue_type
      ORDER BY count DESC
      LIMIT 10
    `);

    // Activity trends (last 30 days)
    const activityTrends = await db.all(`
      SELECT
        activity_type,
        DATE(created_at) as date,
        COUNT(*) as count
      FROM activity_log
      WHERE created_at >= datetime('now', '-30 days')
      GROUP BY activity_type, DATE(created_at)
      ORDER BY date ASC, activity_type
    `);

    // Most active operators (by activity log)
    const mostActiveOperators = await db.all(`
      SELECT
        o.name,
        COUNT(al.id) as activity_count
      FROM operators o
      LEFT JOIN activity_log al ON o.id = al.operator_id
      WHERE al.created_at >= datetime('now', '-30 days')
      GROUP BY o.id, o.name
      ORDER BY activity_count DESC
      LIMIT 5
    `);

    // Tech performance
    const techPerformance = await db.all(`
      SELECT
        t.name,
        COUNT(i.id) as resolved_count,
        AVG(JULIANDAY(i.resolved_at) - JULIANDAY(i.assigned_at)) * 24 as avg_resolution_hours
      FROM techs t
      LEFT JOIN issues i ON t.id = i.assigned_tech_id AND i.status = 'Resolved'
      WHERE i.resolved_at IS NOT NULL AND i.assigned_at IS NOT NULL
      GROUP BY t.id, t.name
      HAVING resolved_count > 0
      ORDER BY resolved_count DESC
      LIMIT 10
    `);

    res.render('admin/reports', {
      title: 'System Reports',
      session: req.session,
      stats: {
        totalIssues: totalIssues.count,
        totalPictures: totalPictures.count,
        totalOperators: totalOperators.count,
        totalMachines: totalMachines.count,
        totalTechs: totalTechs.count,
        dbSize: dbSize,
        avgResolutionHours: avgResolutionTime?.avg_hours ? avgResolutionTime.avg_hours.toFixed(1) : 'N/A',
        systemStatus: systemStatus,
        systemStatusColor: systemStatusColor,
        systemStatusIcon: systemStatusIcon
      },
      charts: {
        issuesByMachineType,
        issuesOverTime,
        weeklyIssues,
        monthlyIssues,
        issueStatusDistribution,
        issuePriorityDistribution,
        topIssueTypes,
        activityTrends,
        topOperatorsByIssues,
        mostActiveOperators,
        techPerformance
      }
    });
  } catch (error) {
    console.error('Reports error:', error);
    res.render('admin/reports', {
      title: 'System Reports',
      session: req.session,
      stats: {
        totalIssues: 0,
        totalPictures: 0,
        totalOperators: 0,
        totalMachines: 0,
        totalTechs: 0,
        dbSize: 'N/A',
        avgResolutionHours: 'N/A',
        systemStatus: 'Error',
        systemStatusColor: 'danger',
        systemStatusIcon: 'bi-x-circle'
      },
      charts: {
        issuesByMachineType: [],
        issuesOverTime: [],
        weeklyIssues: [],
        monthlyIssues: [],
        issueStatusDistribution: [],
        issuePriorityDistribution: [],
        topIssueTypes: [],
        activityTrends: [],
        topOperatorsByIssues: [],
        mostActiveOperators: [],
        techPerformance: []
      },
      error: 'Failed to load reports data'
    });
  }
});

module.exports = router;
