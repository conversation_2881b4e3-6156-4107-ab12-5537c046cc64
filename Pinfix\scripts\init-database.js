const bcrypt = require('bcryptjs');
const database = require('../models/database');
require('dotenv').config();

async function initializeDatabase() {
  try {
    console.log('Initializing database...');
    
    // Connect to database
    await database.connect();
    
    // Create tables
    await database.initializeTables();
    console.log('Database tables created successfully');
    
    // Create super admin user
    const superAdminUsername = process.env.SUPER_ADMIN_USERNAME || 'superadmin';
    const superAdminPassword = process.env.SUPER_ADMIN_PASSWORD || 'admin123';
    
    // Check if super admin already exists
    const existingSuperAdmin = await database.get(
      'SELECT id FROM super_admin WHERE username = ?',
      [superAdminUsername]
    );
    
    if (!existingSuperAdmin) {
      const hashedPassword = await bcrypt.hash(superAdminPassword, 10);
      await database.run(
        'INSERT INTO super_admin (username, password_hash) VALUES (?, ?)',
        [superAdminUsername, hashedPassword]
      );
      console.log(`Super admin created with username: ${superAdminUsername}`);
      console.log(`Super admin password: ${superAdminPassword}`);
      console.log('IMPORTANT: Change the super admin password in production!');
    } else {
      console.log('Super admin already exists');
    }
    
    // Insert some sample global pinball machines
    const sampleMachines = [
      {
        name: 'Medieval Madness',
        manufacturer: 'Williams',
        date_of_manufacture: 1997,
        machine_type: 'SS DMD'
      },
      {
        name: 'Attack from Mars',
        manufacturer: 'Bally',
        date_of_manufacture: 1995,
        machine_type: 'SS DMD'
      },
      {
        name: 'The Mandalorian',
        manufacturer: 'Stern',
        date_of_manufacture: 2021,
        machine_type: 'SS LCD'
      },
      {
        name: 'Pirates of the Caribbean',
        manufacturer: 'Jersey Jack Pinball',
        date_of_manufacture: 2018,
        machine_type: 'SS LCD'
      },
      {
        name: 'Twilight Zone',
        manufacturer: 'Bally',
        date_of_manufacture: 1993,
        machine_type: 'SS DMD'
      }
    ];
    
    for (const machine of sampleMachines) {
      const existing = await database.get(
        'SELECT id FROM global_pinball_machines WHERE name = ? AND manufacturer = ?',
        [machine.name, machine.manufacturer]
      );
      
      if (!existing) {
        await database.run(
          'INSERT INTO global_pinball_machines (name, manufacturer, date_of_manufacture, machine_type) VALUES (?, ?, ?, ?)',
          [machine.name, machine.manufacturer, machine.date_of_manufacture, machine.machine_type]
        );
        console.log(`Added sample machine: ${machine.name} by ${machine.manufacturer}`);
      }
    }
    
    console.log('Database initialization completed successfully!');
    
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  } finally {
    await database.close();
  }
}

// Run initialization if this script is called directly
if (require.main === module) {
  initializeDatabase();
}

module.exports = initializeDatabase;
