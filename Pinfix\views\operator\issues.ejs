<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-bug"></i> Issues Management
                    </h1>
                    <a href="/<%= operator.username %>/admin/machines" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Report New Issue
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <% if (typeof success !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%= success %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Issues Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center filter-card" data-status="Open" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-danger">Open Issues</h5>
                        <h2 class="text-danger"><%= issues.filter(i => i.status === 'Open').length %></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center filter-card" data-status="Assigned" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-warning">In Progress</h5>
                        <h2 class="text-warning"><%= issues.filter(i => i.status === 'Assigned').length %></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center filter-card" data-status="Not Duplicated" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-warning">Not Duplicated</h5>
                        <h2 class="text-warning"><%= issues.filter(i => i.status === 'Not Duplicated').length %></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center filter-card" data-assigned-to="operator" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-primary">My Issues</h5>
                        <h2 class="text-primary"><%= issues.filter(i => i.status === 'Assigned' && !i.assigned_tech_id).length %></h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Issues Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">All Issues</h5>
                        <div>
                            <span id="filter-status" class="badge bg-info me-2" style="display: none;"></span>
                            <button id="clear-filters" class="btn btn-sm btn-outline-secondary" style="display: none;">
                                <i class="bi bi-x-circle"></i> Clear Filters
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <% if (issues.length === 0) { %>
                            <div class="text-center py-5">
                                <i class="bi bi-bug display-1 text-muted"></i>
                                <h4 class="text-muted mt-3">No Issues Reported</h4>
                                <p class="text-muted">Great! All your machines are running smoothly.</p>
                                <a href="/<%= operator.username %>/admin/machines" class="btn btn-primary">
                                    <i class="bi bi-controller"></i> View Machines
                                </a>
                            </div>
                        <% } else { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Machine</th>
                                            <th>Issue Type</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Assigned To</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% issues.forEach(issue => { %>
                                            <tr class="issue-row" data-status="<%= issue.status %>" data-assigned-to="<%= issue.status === 'Assigned' ? (issue.assigned_tech_id ? 'tech' : 'operator') : 'none' %>">
                                                <td>
                                                    <strong>#<%= issue.id %></strong>
                                                </td>
                                                <td>
                                                    <div>
                                                        <%= issue.machine_name %>
                                                        <% if (issue.location_notes) { %>
                                                            <br><small class="text-muted">
                                                                <i class="bi bi-geo-alt"></i> <%= issue.location_notes %>
                                                            </small>
                                                        <% } %>
                                                    </div>
                                                </td>
                                                <td>
                                                    <%= issue.issue_type %>
                                                    <% if (issue.user_comment) { %>
                                                        <br><small class="text-muted"><%= issue.user_comment.substring(0, 50) %><%= issue.user_comment.length > 50 ? '...' : '' %></small>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <% 
                                                    let priorityClass = 'bg-secondary';
                                                    if (issue.priority === 'High') priorityClass = 'bg-danger';
                                                    else if (issue.priority === 'Medium') priorityClass = 'bg-warning';
                                                    else if (issue.priority === 'Low') priorityClass = 'bg-success';
                                                    %>
                                                    <span class="badge <%= priorityClass %>"><%= issue.priority %></span>
                                                </td>
                                                <td>
                                                    <%
                                                    let statusClass = 'bg-warning'; // Default yellow for everything else
                                                    if (issue.status === 'Open') statusClass = 'bg-danger';
                                                    else if (issue.status === 'Resolved') statusClass = 'bg-success';
                                                    %>
                                                    <span class="badge <%= statusClass %>"><%= issue.status %></span>
                                                </td>
                                                <td>
                                                    <% if (issue.assigned_tech_id || issue.assigned_at) { %>
                                                        <% if (issue.assigned_tech_id && issue.tech_name) { %>
                                                            <span class="text-info">
                                                                <i class="bi bi-person"></i> <%= issue.tech_name %>
                                                            </span>
                                                            <br><small class="text-muted">Technician</small>
                                                        <% } else { %>
                                                            <span class="text-primary">
                                                                <i class="bi bi-buildings"></i> <%= operator.name %>
                                                            </span>
                                                            <br><small class="text-muted">Operator</small>
                                                        <% } %>
                                                    <% } else { %>
                                                        <span class="text-muted">
                                                            Not assigned
                                                        </span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <%= issue.formatted_created_date %>
                                                    <br><small class="text-muted"><%= issue.formatted_created_time %></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="/<%= operator.username %>/admin/issues/<%= issue.id %>" 
                                                           class="btn btn-outline-primary">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>
                                                        <a href="/<%= operator.username %>/admin/machines/<%= issue.machine_id %>" 
                                                           class="btn btn-outline-secondary">
                                                            <i class="bi bi-controller"></i> Machine
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Issues Filter JS -->
    <script src="/static/js/issues-filter.js"></script>
</body>
</html>
