// Timezone utility functions

/**
 * Convert UTC date to local time based on timezone offset
 * @param {string} utcDateString - UTC date string from database
 * @param {number} timezoneOffset - Timezone offset in hours (e.g., -5 for EST, +1 for CET)
 * @returns {Date} - Local date object
 */
function convertToLocalTime(utcDateString, timezoneOffset = 0) {
  if (!utcDateString) return null;
  
  const utcDate = new Date(utcDateString);
  if (isNaN(utcDate.getTime())) return null;
  
  // Add timezone offset (convert hours to milliseconds)
  const localDate = new Date(utcDate.getTime() + (timezoneOffset * 60 * 60 * 1000));
  return localDate;
}

/**
 * Format date for display in local timezone
 * @param {string} utcDateString - UTC date string from database
 * @param {number} timezoneOffset - Timezone offset in hours
 * @returns {string} - Formatted date string
 */
function formatLocalDate(utcDateString, timezoneOffset = 0) {
  const localDate = convertToLocalTime(utcDateString, timezoneOffset);
  if (!localDate) return 'Invalid Date';
  
  return localDate.toLocaleDateString();
}

/**
 * Format time for display in local timezone
 * @param {string} utcDateString - UTC date string from database
 * @param {number} timezoneOffset - Timezone offset in hours
 * @returns {string} - Formatted time string
 */
function formatLocalTime(utcDateString, timezoneOffset = 0) {
  const localDate = convertToLocalTime(utcDateString, timezoneOffset);
  if (!localDate) return 'Invalid Time';
  
  return localDate.toLocaleTimeString();
}

/**
 * Format full datetime for display in local timezone
 * @param {string} utcDateString - UTC date string from database
 * @param {number} timezoneOffset - Timezone offset in hours
 * @returns {string} - Formatted datetime string
 */
function formatLocalDateTime(utcDateString, timezoneOffset = 0) {
  const localDate = convertToLocalTime(utcDateString, timezoneOffset);
  if (!localDate) return 'Invalid DateTime';
  
  return localDate.toLocaleString();
}

/**
 * Common timezone options for dropdown
 */
const timezoneOptions = [
  { value: -12, label: 'UTC-12:00 (Baker Island)' },
  { value: -11, label: 'UTC-11:00 (American Samoa)' },
  { value: -10, label: 'UTC-10:00 (Hawaii)' },
  { value: -9, label: 'UTC-09:00 (Alaska)' },
  { value: -8, label: 'UTC-08:00 (Pacific Time)' },
  { value: -7, label: 'UTC-07:00 (Mountain Time)' },
  { value: -6, label: 'UTC-06:00 (Central Time)' },
  { value: -5, label: 'UTC-05:00 (Eastern Time)' },
  { value: -4, label: 'UTC-04:00 (Atlantic Time)' },
  { value: -3, label: 'UTC-03:00 (Argentina)' },
  { value: -2, label: 'UTC-02:00 (Mid-Atlantic)' },
  { value: -1, label: 'UTC-01:00 (Azores)' },
  { value: 0, label: 'UTC+00:00 (London, Dublin)' },
  { value: 1, label: 'UTC+01:00 (Paris, Berlin)' },
  { value: 2, label: 'UTC+02:00 (Cairo, Athens)' },
  { value: 3, label: 'UTC+03:00 (Moscow, Istanbul)' },
  { value: 4, label: 'UTC+04:00 (Dubai, Baku)' },
  { value: 5, label: 'UTC+05:00 (Karachi, Tashkent)' },
  { value: 6, label: 'UTC+06:00 (Dhaka, Almaty)' },
  { value: 7, label: 'UTC+07:00 (Bangkok, Jakarta)' },
  { value: 8, label: 'UTC+08:00 (Beijing, Singapore)' },
  { value: 9, label: 'UTC+09:00 (Tokyo, Seoul)' },
  { value: 10, label: 'UTC+10:00 (Sydney, Melbourne)' },
  { value: 11, label: 'UTC+11:00 (Solomon Islands)' },
  { value: 12, label: 'UTC+12:00 (New Zealand)' }
];

module.exports = {
  convertToLocalTime,
  formatLocalDate,
  formatLocalTime,
  formatLocalDateTime,
  timezoneOptions
};
