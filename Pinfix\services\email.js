const { Resend } = require('resend');

class EmailService {
  constructor() {
    this.resend = new Resend(process.env.RESEND_API_KEY);
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
  }

  /**
   * Send issue notification email to operator
   * @param {Object} issueData - Issue details
   * @param {Object} operatorData - Operator details
   * @param {Object} machineData - Machine details
   */
  async sendIssueNotification(issueData, operatorData, machineData) {
    try {
      // Check if operator has email notifications enabled by super admin
      if (!operatorData.email_notifications_enabled) {
        console.log('Email notifications not enabled for this operator by super admin');
        return;
      }

      // Check if operator has enabled their own notifications
      if (!operatorData.operator_notifications_enabled) {
        console.log('Operator notifications disabled by operator preference');
        return;
      }

      if (!operatorData.email) {
        console.log('No operator email found, skipping notification');
        return;
      }

      const subject = `New ${issueData.priority} Priority Issue: ${issueData.issue_type} - ${machineData.name}`;

      const htmlContent = this.generateIssueEmailHTML(issueData, operatorData, machineData);

      const result = await this.resend.emails.send({
        from: `PinFix <${this.fromEmail}>`,
        to: [operatorData.email],
        subject: subject,
        html: htmlContent,
      });

      console.log('✅ Issue notification email sent successfully:', result.id);
      return result;
    } catch (error) {
      console.error('❌ Failed to send issue notification email:', error);
      // Don't throw error to avoid breaking the main issue creation flow
      return null;
    }
  }

  /**
   * Generate HTML content for issue notification email
   * @param {Object} issueData - Issue details
   * @param {Object} operatorData - Operator details  
   * @param {Object} machineData - Machine details
   * @returns {string} HTML email content
   */
  generateIssueEmailHTML(issueData, operatorData, machineData) {
    const priorityColor = {
      'High': '#dc3545',
      'Medium': '#ffc107', 
      'Low': '#28a745'
    };

    const priorityBadgeColor = priorityColor[issueData.priority] || '#6c757d';

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Issue Report - PinFix</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #0d6efd; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .priority-badge { 
                display: inline-block; 
                padding: 4px 12px; 
                border-radius: 20px; 
                color: white; 
                font-weight: bold; 
                background-color: ${priorityBadgeColor};
            }
            .machine-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0d6efd; }
            .issue-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .button { 
                display: inline-block; 
                padding: 12px 24px; 
                background: #0d6efd; 
                color: white; 
                text-decoration: none; 
                border-radius: 6px; 
                margin: 20px 0;
            }
            .footer { text-align: center; margin-top: 30px; color: #6c757d; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔧 New Issue Report</h1>
                <p>A customer has reported an issue with one of your pinball machines</p>
            </div>
            
            <div class="content">
                <div class="machine-info">
                    <h3>🎮 Machine Information</h3>
                    <p><strong>Machine:</strong> ${machineData.name}</p>
                    <p><strong>Manufacturer:</strong> ${machineData.manufacturer}</p>
                    <p><strong>Operator:</strong> ${operatorData.name}</p>
                    ${machineData.location_notes ? `<p><strong>Location:</strong> ${machineData.location_notes}</p>` : ''}
                </div>

                <div class="issue-details">
                    <h3>⚠️ Issue Details</h3>
                    <p><strong>Issue Type:</strong> ${issueData.issue_type}</p>
                    <p><strong>Priority:</strong> <span class="priority-badge">${issueData.priority}</span></p>
                    
                    ${issueData.user_comment ? `
                    <p><strong>Customer Comments:</strong></p>
                    <p style="background: #e9ecef; padding: 15px; border-radius: 6px; font-style: italic;">
                        "${issueData.user_comment}"
                    </p>
                    ` : ''}
                    
                    ${issueData.user_picture_path ? `
                    <p><strong>Photo:</strong> Customer attached a photo with this report</p>
                    ` : ''}
                    
                    <p><strong>Reported:</strong> ${new Date(issueData.created_at).toLocaleString()}</p>
                </div>

                <div style="text-align: center;">
                    <a href="${process.env.BASE_URL || 'http://localhost:3000'}/${operatorData.username}/admin/issues" class="button">
                        View Issue in Dashboard
                    </a>
                </div>

                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #dee2e6;">
                    <h4>📋 Next Steps:</h4>
                    <ul>
                        <li>Review the issue details in your operator dashboard</li>
                        <li>Assign the issue to yourself or a technician</li>
                        <li>Update the status as you work on resolving the issue</li>
                        <li>Mark as resolved when the machine is fixed</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>This email was sent by PinFix - Professional Pinball Issue Tracking</p>
                <p>Keep your machines flipping, not flopping!</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Send tech assignment notification email
   * @param {Object} issueData - Issue details
   * @param {Object} techData - Tech details
   * @param {Object} operatorData - Operator details
   * @param {Object} machineData - Machine details
   */
  async sendTechAssignmentNotification(issueData, techData, operatorData, machineData) {
    try {
      // Check if operator has email notifications enabled by super admin
      if (!operatorData.email_notifications_enabled) {
        console.log('Email notifications not enabled for this operator by super admin');
        return;
      }

      // Check if operator has enabled tech notifications
      if (!operatorData.tech_notifications_enabled) {
        console.log('Tech notifications disabled by operator preference');
        return;
      }

      if (!techData.email) {
        console.log('No tech email found, skipping tech notification');
        return;
      }

      const subject = `Issue Assigned: ${issueData.issue_type} - ${machineData.name}`;

      const htmlContent = this.generateTechAssignmentEmailHTML(issueData, techData, operatorData, machineData);

      const result = await this.resend.emails.send({
        from: `PinFix <${this.fromEmail}>`,
        to: [techData.email],
        subject: subject,
        html: htmlContent,
      });

      console.log('✅ Tech assignment notification email sent successfully:', result.id);
      return result;
    } catch (error) {
      console.error('❌ Failed to send tech assignment notification email:', error);
      // Don't throw error to avoid breaking the main assignment flow
      return null;
    }
  }

  /**
   * Generate HTML content for tech assignment notification email
   * @param {Object} issueData - Issue details
   * @param {Object} techData - Tech details
   * @param {Object} operatorData - Operator details
   * @param {Object} machineData - Machine details
   * @returns {string} HTML email content
   */
  generateTechAssignmentEmailHTML(issueData, techData, operatorData, machineData) {
    const priorityColor = {
      'High': '#dc3545',
      'Medium': '#ffc107',
      'Low': '#28a745'
    };

    const priorityBadgeColor = priorityColor[issueData.priority] || '#6c757d';

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Issue Assigned - PinFix</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #0dcaf0; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 8px 8px; }
            .priority-badge {
                display: inline-block;
                padding: 4px 12px;
                border-radius: 20px;
                color: white;
                font-weight: bold;
                background-color: ${priorityBadgeColor};
            }
            .machine-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0dcaf0; }
            .issue-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .button {
                display: inline-block;
                padding: 12px 24px;
                background: #0dcaf0;
                color: white;
                text-decoration: none;
                border-radius: 6px;
                margin: 20px 0;
            }
            .footer { text-align: center; margin-top: 30px; color: #6c757d; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔧 Issue Assigned to You</h1>
                <p>You have been assigned a new issue to resolve</p>
            </div>

            <div class="content">
                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #0dcaf0;">
                    <h3>👋 Hello ${techData.name}!</h3>
                    <p>You have been assigned a new issue by <strong>${operatorData.name}</strong>. Please review the details below and take appropriate action.</p>
                </div>

                <div class="machine-info">
                    <h3>🎮 Machine Information</h3>
                    <p><strong>Machine:</strong> ${machineData.name}</p>
                    <p><strong>Manufacturer:</strong> ${machineData.manufacturer}</p>
                    <p><strong>Operator:</strong> ${operatorData.name}</p>
                    ${machineData.location_notes ? `<p><strong>Location:</strong> ${machineData.location_notes}</p>` : ''}
                </div>

                <div class="issue-details">
                    <h3>⚠️ Issue Details</h3>
                    <p><strong>Issue Type:</strong> ${issueData.issue_type}</p>
                    <p><strong>Priority:</strong> <span class="priority-badge">${issueData.priority}</span></p>

                    ${issueData.user_comment ? `
                    <p><strong>Customer Comments:</strong></p>
                    <p style="background: #e9ecef; padding: 15px; border-radius: 6px; font-style: italic;">
                        "${issueData.user_comment}"
                    </p>
                    ` : ''}

                    ${issueData.user_picture_path ? `
                    <p><strong>Photo:</strong> Customer attached a photo with this report</p>
                    ` : ''}

                    <p><strong>Originally Reported:</strong> ${new Date(issueData.created_at).toLocaleString()}</p>
                    <p><strong>Assigned:</strong> ${new Date().toLocaleString()}</p>
                </div>

                <div style="text-align: center;">
                    <a href="${process.env.BASE_URL || 'http://localhost:3000'}/${operatorData.username}/tech/issues/${issueData.id}" class="button">
                        View Issue Details
                    </a>
                </div>

                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #dee2e6;">
                    <h4>📋 Your Next Steps:</h4>
                    <ul>
                        <li>Review the issue details and customer comments</li>
                        <li>Visit the machine location to assess the problem</li>
                        <li>Update the issue status as you work on it</li>
                        <li>Mark as resolved when the machine is fixed</li>
                        <li>Add any technical notes for future reference</li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>This email was sent by PinFix - Professional Pinball Issue Tracking</p>
                <p>Keep your machines flipping, not flopping!</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  /**
   * Send test email (for debugging)
   * @param {string} toEmail - Recipient email
   */
  async sendTestEmail(toEmail) {
    try {
      const result = await this.resend.emails.send({
        from: `PinFix <${this.fromEmail}>`,
        to: [toEmail],
        subject: 'PinFix Email Test',
        html: '<p>This is a test email from PinFix. Email notifications are working correctly!</p>',
      });

      console.log('✅ Test email sent successfully:', result.id);
      return result;
    } catch (error) {
      console.error('❌ Failed to send test email:', error);
      throw error;
    }
  }
}

module.exports = new EmailService();
