"""
Test script to verify progress bar functionality
"""

import time
from data_extractor import DataExtractor


def test_progress_callback():
    """Test the progress callback functionality"""
    print("Testing progress callback...")
    
    if not os.path.exists("OwnShipRecorder.tzdb"):
        print("No test database available")
        return
    
    extractor = DataExtractor("OwnShipRecorder.tzdb")
    
    # Test progress callback
    progress_messages = []
    
    def progress_callback(message):
        progress_messages.append(message)
        print(f"Progress: {message}")
    
    print("\nExtracting small sample with progress...")
    # Extract just a small amount of data to test progress
    records = extractor.get_data_for_timeframe(1, progress_callback)  # Last hour
    
    print(f"\nExtracted {len(records)} records")
    print(f"Progress messages received: {len(progress_messages)}")
    for i, msg in enumerate(progress_messages):
        print(f"  {i+1}: {msg}")


if __name__ == "__main__":
    import os
    test_progress_callback()
