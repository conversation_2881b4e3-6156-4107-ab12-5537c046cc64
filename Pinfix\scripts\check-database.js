const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'pinfix.db');

// Open database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
    process.exit(1);
  }
  console.log('Connected to SQLite database');
});

// Check what tables exist
db.serialize(() => {
  db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
    if (err) {
      console.error('Error getting tables:', err.message);
      process.exit(1);
    }
    
    console.log('Existing tables:');
    tables.forEach(table => {
      console.log('- ' + table.name);
    });
    
    // Check if issues table exists
    const issuesTable = tables.find(t => t.name === 'issues');
    if (issuesTable) {
      console.log('\nIssues table schema:');
      db.all("PRAGMA table_info(issues)", (err, columns) => {
        if (err) {
          console.error('Error getting issues table info:', err.message);
        } else {
          columns.forEach(col => {
            console.log(`- ${col.name}: ${col.type} (${col.notnull ? 'NOT NULL' : 'NULL'}) ${col.dflt_value ? 'DEFAULT ' + col.dflt_value : ''}`);
          });
        }
        db.close();
      });
    } else {
      console.log('\nIssues table does not exist');
      db.close();
    }
  });
});
