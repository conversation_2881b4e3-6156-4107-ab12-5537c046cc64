const bcrypt = require('bcryptjs');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'pinfix.db');

// Open database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
    process.exit(1);
  }
  console.log('Connected to SQLite database');
});

async function addTestData() {
  return new Promise((resolve, reject) => {
    db.serialize(async () => {
      try {
        console.log('Adding test data...');

        // Add global machines first
        const globalMachines = [
          { name: 'Medieval Madness', manufacturer: 'Williams', date_of_manufacture: 1997, machine_type: 'SS DMD' },
          { name: 'Attack from Mars', manufacturer: 'Bally', date_of_manufacture: 1995, machine_type: 'SS DMD' },
          { name: 'The Addams Family', manufacturer: '<PERSON>y', date_of_manufacture: 1992, machine_type: 'SS DMD' },
          { name: 'Twilight Zone', manufacturer: 'Bally', date_of_manufacture: 1993, machine_type: 'SS DMD' }
        ];

        for (const machine of globalMachines) {
          await new Promise((resolve, reject) => {
            db.run(
              'INSERT OR IGNORE INTO global_pinball_machines (name, manufacturer, date_of_manufacture, machine_type) VALUES (?, ?, ?, ?)',
              [machine.name, machine.manufacturer, machine.date_of_manufacture, machine.machine_type],
              function(err) {
                if (err) reject(err);
                else {
                  console.log(`Added global machine: ${machine.name}`);
                  resolve();
                }
              }
            );
          });
        }

        // Add test operator
        const hashedPassword = await bcrypt.hash('password123', 10);
        await new Promise((resolve, reject) => {
          db.run(
            'INSERT OR IGNORE INTO operators (name, username, password_hash, email, status) VALUES (?, ?, ?, ?, ?)',
            ['BAT Pinball', 'bat', hashedPassword, '<EMAIL>', 'active'],
            function(err) {
              if (err) reject(err);
              else {
                console.log('Added test operator: BAT Pinball');
                resolve();
              }
            }
          );
        });

        // Get operator ID
        const operator = await new Promise((resolve, reject) => {
          db.get('SELECT id FROM operators WHERE username = ?', ['bat'], (err, row) => {
            if (err) reject(err);
            else resolve(row);
          });
        });

        if (!operator) {
          throw new Error('Failed to create operator');
        }

        console.log('Operator ID:', operator.id);

        // Get global machine IDs
        const globalMachineIds = await new Promise((resolve, reject) => {
          db.all('SELECT id, name FROM global_pinball_machines', (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          });
        });

        console.log('Global machines found:', globalMachineIds.length);

        // Add pinball machines for the operator
        const machineLocations = [
          'Front entrance area',
          'Main game room',
          'Back corner',
          'Near the bar'
        ];

        for (let i = 0; i < Math.min(globalMachineIds.length, machineLocations.length); i++) {
          const globalMachine = globalMachineIds[i];
          const location = machineLocations[i];
          
          await new Promise((resolve, reject) => {
            db.run(
              'INSERT INTO pinball_machines (operator_id, global_machine_id, location_notes, status) VALUES (?, ?, ?, ?)',
              [operator.id, globalMachine.id, location, 'active'],
              function(err) {
                if (err) reject(err);
                else {
                  console.log(`Added machine: ${globalMachine.name} at ${location}`);
                  resolve();
                }
              }
            );
          });
        }

        console.log('Test data added successfully!');
        resolve();
      } catch (error) {
        console.error('Error adding test data:', error);
        reject(error);
      }
    });
  });
}

addTestData()
  .then(() => {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err.message);
      } else {
        console.log('Database closed successfully');
      }
    });
  })
  .catch((error) => {
    console.error('Failed to add test data:', error);
    process.exit(1);
  });
