<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="/static/images/pinfix-logo.svg" alt="PinFix" style="height: 32px; width: 32px; margin-right: 8px;">
                PinFix
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="issue-form">
            <div class="text-center mb-4">
                <% if (operator.logo_path) { %>
                    <img src="/uploads/<%= operator.logo_path %>" alt="<%= operator.company_name || operator.account_name %>" class="mb-3" style="max-height: 80px;">
                <% } %>

                <h2>Report an Issue</h2>
                <p class="text-muted">Help us keep this pinball machine in perfect condition</p>
            </div>

            <form method="POST" action="/<%= operator.account_name %>/report/<%= machineId %>" enctype="multipart/form-data" data-validate>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">What's the problem?</h5>
            </div>
            <div class="card-body">
                <div class="issue-type-grid">
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Flipper not working" required>
                            <i class="bi bi-x-circle text-danger fs-1"></i>
                            <h6 class="mt-2">Flipper not working</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Flipper weak or Sticking" required>
                            <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
                            <h6 class="mt-2">Flipper weak or sticking</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Ball not ejecting" required>
                            <i class="bi bi-pause-circle text-danger fs-1"></i>
                            <h6 class="mt-2">Ball not ejecting</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Ball Stuck on Playfield" required>
                            <i class="bi bi-stop-circle text-danger fs-1"></i>
                            <h6 class="mt-2">Ball stuck on playfield</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Multiple balls ejecting unexpectedly" required>
                            <i class="bi bi-arrow-repeat text-warning fs-1"></i>
                            <h6 class="mt-2">Multiple balls ejecting</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Pop bumper not firing" required>
                            <i class="bi bi-circle text-danger fs-1"></i>
                            <h6 class="mt-2">Pop bumper not firing</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Switch not registering hits" required>
                            <i class="bi bi-toggle-off text-danger fs-1"></i>
                            <h6 class="mt-2">Switch not working</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="General illumination out" required>
                            <i class="bi bi-lightbulb text-warning fs-1"></i>
                            <h6 class="mt-2">Lights not working</h6>
                        </div>
                    </div>
                    
                    <div class="issue-type-card card">
                        <div class="card-body text-center">
                            <input type="radio" name="issueType" value="Sound distorted" required>
                            <i class="bi bi-volume-mute text-warning fs-1"></i>
                            <h6 class="mt-2">Sound problems</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Additional Details</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="userComment" class="form-label">Describe the issue (optional)</label>
                    <textarea class="form-control" id="userComment" name="userComment" rows="3" 
                              placeholder="Please provide any additional details about the problem..."></textarea>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Add a photo (optional)</label>
                    <div class="image-upload-area" id="imageUploadArea">
                        <i class="bi bi-camera fs-1 text-muted"></i>
                        <p class="mt-2 mb-0">Tap to take a photo or upload an image</p>
                        <small class="text-muted">This helps our technicians understand the problem</small>
                    </div>
                    <input type="file" id="imageFile" name="userPicture" accept="image/*" capture="environment" style="display: none;">
                    <img id="imagePreview" class="image-preview" style="display: none;">
                </div>
            </div>
        </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="bi bi-send"></i> Submit Issue Report
                </button>
                <a href="/<%= operator.account_name %>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Cancel
                </a>
            </div>
            </form>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
</body>
</html>
