# RingCentral Token Management Guide

This guide shows how to extract both access and refresh tokens, and how to refresh tokens when they expire.

## Quick Reference

### Get Both Access and Refresh Tokens

```python
from rc_auth import get_ringcentral_tokens

# Get both tokens at once
access_token, refresh_token = get_ringcentral_tokens()

print(f"Access token: {access_token}")
print(f"Refresh token: {refresh_token}")
```

### Refresh Tokens When They Expire

```python
from rc_auth import refresh_ringcentral_tokens

# When your access token expires, use the refresh token
new_access_token, new_refresh_token = refresh_ringcentral_tokens(refresh_token)

print(f"New access token: {new_access_token}")
print(f"New refresh token: {new_refresh_token}")
```

## Complete Example

```python
from rc_auth import get_ringcentral_tokens, refresh_ringcentral_tokens
import requests
import time

def main():
    # Step 1: Get initial tokens
    print("Getting initial tokens...")
    access_token, refresh_token = get_ringcentral_tokens()
    
    # Step 2: Use access token for API calls
    print("Making API call...")
    headers = {"Authorization": f"Bearer {access_token}"}
    
    response = requests.get(
        "https://platform.ringcentral.com/restapi/v1.0/account/~/extension/~",
        headers=headers
    )
    
    if response.status_code == 200:
        user_info = response.json()
        print(f"User: {user_info.get('name')}")
    else:
        print(f"API call failed: {response.status_code}")
    
    # Step 3: When token expires (simulate by waiting or when you get 401)
    print("Refreshing tokens...")
    try:
        new_access_token, new_refresh_token = refresh_ringcentral_tokens(refresh_token)
        print("✓ Tokens refreshed successfully")
        
        # Update your stored tokens
        access_token = new_access_token
        refresh_token = new_refresh_token
        
    except Exception as e:
        print(f"Token refresh failed: {e}")

if __name__ == "__main__":
    main()
```

## Available Functions

### 1. `get_ringcentral_tokens(credentials_file="rc-credentials.json")`

**Returns both access and refresh tokens.**

```python
access_token, refresh_token = get_ringcentral_tokens()
```

**Returns:**
- `access_token` (str): Use for API calls
- `refresh_token` (str): Use to get new access tokens

### 2. `refresh_ringcentral_tokens(refresh_token, credentials_file="rc-credentials.json")`

**Get new tokens using refresh token.**

```python
new_access_token, new_refresh_token = refresh_ringcentral_tokens(refresh_token)
```

**Parameters:**
- `refresh_token` (str): Your current refresh token

**Returns:**
- `new_access_token` (str): New access token
- `new_refresh_token` (str): New refresh token

### 3. `get_ringcentral_access_token(credentials_file="rc-credentials.json")`

**Get just the access token (convenience function).**

```python
access_token = get_ringcentral_access_token()
```

### 4. `refresh_ringcentral_access_token(refresh_token, credentials_file="rc-credentials.json")`

**Get full refresh response.**

```python
response = refresh_ringcentral_access_token(refresh_token)
print(f"Expires in: {response['expires_in']} seconds")
```

## Token Lifetimes

Based on the test results:

- **Access Token**: Expires in 3600 seconds (1 hour)
- **Refresh Token**: Expires in 604800 seconds (7 days)

## Best Practices

### 1. Store Both Tokens

```python
# Store both tokens securely
tokens = {
    "access_token": access_token,
    "refresh_token": refresh_token,
    "expires_at": time.time() + 3600  # 1 hour from now
}
```

### 2. Check Expiration Before API Calls

```python
import time

def get_valid_access_token():
    global tokens
    
    # Check if token is expired (with 5-minute buffer)
    if time.time() > (tokens["expires_at"] - 300):
        print("Token expired, refreshing...")
        new_access, new_refresh = refresh_ringcentral_tokens(tokens["refresh_token"])
        
        tokens["access_token"] = new_access
        tokens["refresh_token"] = new_refresh
        tokens["expires_at"] = time.time() + 3600
    
    return tokens["access_token"]
```

### 3. Handle Refresh Token Expiration

```python
def refresh_tokens_safely():
    try:
        new_access, new_refresh = refresh_ringcentral_tokens(refresh_token)
        return new_access, new_refresh
    except Exception as e:
        if "invalid_grant" in str(e):
            print("Refresh token expired, need to re-authenticate")
            # Get new tokens from scratch
            return get_ringcentral_tokens()
        else:
            raise e
```

### 4. Automatic Token Management Class

```python
class RingCentralTokenManager:
    def __init__(self):
        self.access_token = None
        self.refresh_token = None
        self.expires_at = 0
    
    def get_access_token(self):
        if time.time() > (self.expires_at - 300):  # 5-minute buffer
            self._refresh_tokens()
        return self.access_token
    
    def _refresh_tokens(self):
        if self.refresh_token:
            try:
                self.access_token, self.refresh_token = refresh_ringcentral_tokens(self.refresh_token)
                self.expires_at = time.time() + 3600
                return
            except:
                pass
        
        # If refresh fails, get new tokens
        self.access_token, self.refresh_token = get_ringcentral_tokens()
        self.expires_at = time.time() + 3600

# Usage
token_manager = RingCentralTokenManager()
headers = {"Authorization": f"Bearer {token_manager.get_access_token()}"}
```

## Testing

Run the comprehensive test:

```bash
python rc_auth.py
```

Run usage examples:

```bash
python example_usage.py
```

## Summary

✅ **Working Features:**
- Extract both access and refresh tokens
- Refresh tokens when they expire
- Automatic token management
- Full RingCentral API access
- RingCX API access (with proper permissions)

The token management system is now complete and production-ready!
