apiVersion: v1
kind: Service
metadata:
  name: call-analysis-service
  labels:
    app: call-analysis
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 5000
    protocol: TCP
    name: http
  selector:
    app: call-analysis

---
# Optional: LoadBalancer service for cloud environments
apiVersion: v1
kind: Service
metadata:
  name: call-analysis-loadbalancer
  labels:
    app: call-analysis
  annotations:
    # Add cloud-specific annotations here if needed
    # For example, for AWS:
    # service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    # For Azure:
    # service.beta.kubernetes.io/azure-load-balancer-internal: "false"
    # For GCP:
    # cloud.google.com/load-balancer-type: "External"
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 5000
    protocol: TCP
    name: http
  selector:
    app: call-analysis
