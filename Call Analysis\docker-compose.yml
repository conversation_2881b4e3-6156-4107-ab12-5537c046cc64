version: '3.8'

services:
  call-analysis:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: call-analysis-app
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_APP=app.py
      - PYTHONUNBUFFERED=1
      - DATABASE_PATH=/app/data/call_analysis.db
      - REPORTS_PATH=/app/reports
      - RINGCX_BASE_URL=https://ringcx.ringcentral.com/voice
    volumes:
      # Persistent data storage
      - call_analysis_data:/app/data
      - call_analysis_reports:/app/reports
      
      # API keys (create these files locally)
      - ./Assembly_KEY.txt:/app/Assembly_KEY.txt:ro
      - ./OpenAI_KEY.txt:/app/OpenAI_KEY.txt:ro
      - ./rc-credentials.json:/app/rc-credentials.json:ro
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Security
    user: "1000:1000"
    read_only: false
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  call_analysis_data:
    driver: local
  call_analysis_reports:
    driver: local

# Optional: Add a reverse proxy for production
# nginx:
#   image: nginx:alpine
#   container_name: call-analysis-nginx
#   ports:
#     - "80:80"
#     - "443:443"
#   volumes:
#     - ./nginx.conf:/etc/nginx/nginx.conf:ro
#     - ./ssl:/etc/nginx/ssl:ro
#   depends_on:
#     - call-analysis
#   restart: unless-stopped
