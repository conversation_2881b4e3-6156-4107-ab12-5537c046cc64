const express = require('express');
const { loadOperatorContext } = require('../middleware/auth');

const router = express.Router();

// Apply operator context loading to routes with :operatorAccount
router.use('/:operatorAccount', loadOperatorContext);

// Issue reporting page (accessed via QR code)
router.get('/:operatorAccount/report/:machineId', (req, res) => {
  const { operatorAccount, machineId } = req.params;
  
  res.render('issues/report', { 
    title: 'Report Issue',
    operator: req.operatorContext,
    machineId: machineId,
    session: req.session
  });
});

module.exports = router;
