/* Custom styles for Pinfix Repair */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

/* Issue reporting styles */
.issue-form {
    max-width: 600px;
    margin: 0 auto;
}

.issue-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.issue-type-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.issue-type-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.issue-type-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

.issue-type-card input[type="radio"] {
    display: none;
}

/* Image upload styles */
.image-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.2s ease;
    cursor: pointer;
}

.image-upload-area:hover {
    border-color: var(--primary-color);
}

.image-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

/* QR Code styles */
.qr-code-container {
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.qr-code-container img {
    max-width: 200px;
    height: auto;
}

/* Issue Photo styles */
.issue-photo-thumbnail {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 0.375rem;
    object-fit: cover;
}

.issue-photo-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.photo-preview-container {
    display: inline-block;
}

/* Photo modal styles */
.modal-lg .modal-body img {
    border-radius: 0.375rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Dashboard styles */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 0.5rem;
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
}

/* Issue status badges */
.status-open {
    background-color: var(--danger-color);
}

.status-assigned {
    background-color: var(--warning-color);
    color: #000;
}

.status-resolved {
    background-color: var(--success-color);
}

.status-not-duplicated {
    background-color: var(--secondary-color);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .issue-type-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Loading spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-border-lg {
    width: 3rem;
    height: 3rem;
}

/* Print styles for QR codes */
@media print {
    body * {
        visibility: hidden;
    }
    
    .qr-code-print, .qr-code-print * {
        visibility: visible;
    }
    
    .qr-code-print {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}
