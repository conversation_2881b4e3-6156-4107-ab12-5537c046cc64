<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-tools"></i> PinFix - Super Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/operators">Operators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/machines">Global Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/reports">Reports</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> Super Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-controller"></i> Global Pinball Machines</h1>
                    <a href="/admin/machines/new" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add New Machine
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <% if (typeof success !== 'undefined' || typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <% if (typeof success !== 'undefined') { %>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <%= success %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                    <% if (typeof error !== 'undefined') { %>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <%= error %>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <% } %>
                </div>
            </div>
        <% } %>

        <!-- Machines Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Pinball Machines</h5>
                    </div>
                    <div class="card-body">
                        <% if (machines.length === 0) { %>
                            <div class="text-center py-4">
                                <i class="bi bi-controller display-1 text-muted"></i>
                                <h4 class="text-muted mt-3">No Machines Found</h4>
                                <p class="text-muted">Add your first pinball machine to get started.</p>
                                <a href="/admin/machines/new" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Add First Machine
                                </a>
                            </div>
                        <% } else { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Machine Name</th>
                                            <th>Manufacturer</th>
                                            <th>Year</th>
                                            <th>Type</th>
                                            <th>Used By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% machines.forEach(machine => { %>
                                            <tr>
                                                <td>
                                                    <strong><%= machine.name %></strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary"><%= machine.manufacturer %></span>
                                                </td>
                                                <td>
                                                    <%= machine.date_of_manufacture || 'Unknown' %>
                                                </td>
                                                <td>
                                                    <% 
                                                    let typeClass = 'bg-info';
                                                    if (machine.machine_type === 'EM') typeClass = 'bg-warning';
                                                    else if (machine.machine_type === 'SS Alpha') typeClass = 'bg-primary';
                                                    else if (machine.machine_type === 'SS DMD') typeClass = 'bg-success';
                                                    else if (machine.machine_type === 'SS LCD') typeClass = 'bg-danger';
                                                    %>
                                                    <span class="badge <%= typeClass %>"><%= machine.machine_type %></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><%= machine.operator_count %> operator<%= machine.operator_count !== 1 ? 's' : '' %></span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="/admin/machines/<%= machine.id %>/edit" 
                                                           class="btn btn-outline-primary" title="Edit">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>

        <!-- Machine Statistics -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Total Machines</h5>
                        <h2 class="text-primary"><%= machines.length %></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Manufacturers</h5>
                        <h2 class="text-success">
                            <%= [...new Set(machines.map(m => m.manufacturer))].length %>
                        </h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Modern (LCD)</h5>
                        <h2 class="text-danger">
                            <%= machines.filter(m => m.machine_type === 'SS LCD').length %>
                        </h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Classic (DMD)</h5>
                        <h2 class="text-warning">
                            <%= machines.filter(m => m.machine_type === 'SS DMD').length %>
                        </h2>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>

    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Handle URL parameters for success/error messages
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('success')) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${urlParams.get('success')}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('main .container-fluid').insertBefore(alertDiv, document.querySelector('main .container-fluid').firstChild);
        }
        if (urlParams.get('error')) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${urlParams.get('error')}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('main .container-fluid').insertBefore(alertDiv, document.querySelector('main .container-fluid').firstChild);
        }
    </script>
</body>
</html>
