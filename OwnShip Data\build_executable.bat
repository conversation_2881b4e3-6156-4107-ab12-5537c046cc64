@echo off
echo Own Ship Extractor - Executable Builder
echo =====================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

echo Python found. Building executable...

REM Install required packages
echo Installing required packages...
pip install pyinstaller matplotlib pandas tkcalendar

REM Build the executable
echo Building executable with PyInstaller...
pyinstaller --onefile --windowed --name "OwnShipExtractor" --add-data "MercatorTools.cs;." own_ship_extractor.py

if exist "dist\OwnShipExtractor.exe" (
    echo.
    echo ✅ Build completed successfully!
    echo Executable created: dist\OwnShipExtractor.exe
    
    REM Create distribution folder
    if exist "OwnShipExtractor_Distribution" rmdir /s /q "OwnShipExtractor_Distribution"
    mkdir "OwnShipExtractor_Distribution"
    
    REM Copy files
    copy "dist\OwnShipExtractor.exe" "OwnShipExtractor_Distribution\"
    copy "README.md" "OwnShipExtractor_Distribution\" 2>nul
    copy "extract_ownship_data.bat" "OwnShipExtractor_Distribution\" 2>nul
    
    echo.
    echo 📦 Distribution package created: OwnShipExtractor_Distribution\
    echo Ready for distribution!
    
    REM Open the distribution folder
    explorer "OwnShipExtractor_Distribution"
    
) else (
    echo.
    echo ❌ Build failed. Check the output above for errors.
)

echo.
echo Build process completed.
pause
