"""
Own Ship Extractor - Main GUI Application
Extracts and visualizes data from OwnShipRecorder.tzdb databases
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import datetime
import os
import sys
import threading
from pathlib import Path

# Import our custom modules
from database_manager import DatabaseManager
from data_extractor import DataExtractor, OwnShipRecord


class OwnShipExtractorGUI:
    """Main GUI application for Own Ship Extractor"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Own Ship Extractor")
        self.root.geometry("1200x800")
        
        # Initialize components
        self.db_manager = DatabaseManager()
        self.data_extractor = None
        self.current_data = []
        self.use_local_time = tk.BooleanVar(value=False)
        self.database_paths = {}  # Mapping of display names to file paths
        
        # Graph parameters
        self.graph_param_a = tk.StringVar(value="Speed Over Ground")
        self.graph_param_b = tk.StringVar(value="Depth")
        
        self.setup_ui()
        self.detect_databases()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Setup tabs
        self.setup_database_tab()
        self.setup_data_tab()
        self.setup_graph_tab()
    
    def setup_database_tab(self):
        """Setup database selection tab"""
        db_frame = ttk.Frame(self.notebook)
        self.notebook.add(db_frame, text="Database Selection")
        
        # Database selection
        ttk.Label(db_frame, text="Select OwnShip Database:", font=("Arial", 12, "bold")).pack(pady=10)
        
        self.db_var = tk.StringVar()
        self.db_combo = ttk.Combobox(db_frame, textvariable=self.db_var, width=80, state="readonly")
        self.db_combo.pack(pady=5)
        self.db_combo.bind("<<ComboboxSelected>>", self.on_database_selected)
        
        # Manual selection button
        ttk.Button(db_frame, text="Select Database File...", 
                  command=self.select_database_file).pack(pady=5)
        
        # Database info
        self.db_info_frame = ttk.LabelFrame(db_frame, text="Database Information")
        self.db_info_frame.pack(fill=tk.X, padx=20, pady=20)
        
        self.db_info_text = tk.Text(self.db_info_frame, height=8, wrap=tk.WORD)
        self.db_info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Date range selection
        date_frame = ttk.LabelFrame(db_frame, text="Date Range Selection")
        date_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.date_option = tk.StringVar(value="Last day")
        date_options = ["Last day", "Last 7 days", "Last month", "Date Range..."]
        
        for option in date_options:
            ttk.Radiobutton(date_frame, text=option, variable=self.date_option, 
                           value=option).pack(anchor=tk.W, padx=10, pady=2)
        
        # Load data button
        self.load_button = ttk.Button(db_frame, text="Load Data", command=self.load_data,
                                     style="Accent.TButton")
        self.load_button.pack(pady=20)

        # Progress bar (initially hidden)
        self.progress_frame = ttk.Frame(db_frame)
        self.progress_frame.pack(fill=tk.X, padx=20, pady=10)

        self.progress_label = ttk.Label(self.progress_frame, text="Loading data...")
        self.progress_label.pack()

        self.progress_bar = ttk.Progressbar(self.progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=5)

        # Hide progress frame initially
        self.progress_frame.pack_forget()
    
    def setup_data_tab(self):
        """Setup data display tab"""
        data_frame = ttk.Frame(self.notebook)
        self.notebook.add(data_frame, text="Data List")
        
        # Options frame
        options_frame = ttk.Frame(data_frame)
        options_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Checkbutton(options_frame, text="Use Local Time", 
                       variable=self.use_local_time,
                       command=self.refresh_data_display).pack(side=tk.LEFT)
        
        ttk.Button(options_frame, text="Export CSV...", 
                  command=self.export_csv).pack(side=tk.RIGHT)
        
        # Data table
        self.setup_data_table(data_frame)
    
    def setup_data_table(self, parent):
        """Setup the data table with scrollbars"""
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create treeview with scrollbars
        self.data_tree = ttk.Treeview(table_frame)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack scrollbars and treeview
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.data_tree.pack(fill=tk.BOTH, expand=True)
        
        # Define columns
        columns = ['Date & Time', 'Latitude', 'Longitude', 'Course Over Ground', 'Heading',
                  'Speed Through Water', 'Speed Over Ground', 'Depth', 'Sea Surface Temperature',
                  'True Wind Direction', 'True Wind Speed', 'Apparent Wind Angle', 
                  'Apparent Wind Speed', 'Air Temperature', 'Atmospheric Pressure', 
                  'Humidity', 'Roll', 'Pitch', 'Heave']
        
        self.data_tree['columns'] = columns
        self.data_tree['show'] = 'headings'
        
        # Configure columns
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=120, minwidth=80)
    
    def setup_graph_tab(self):
        """Setup graph display tab"""
        graph_frame = ttk.Frame(self.notebook)
        self.notebook.add(graph_frame, text="Graph")
        
        # Graph controls
        controls_frame = ttk.Frame(graph_frame)
        controls_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Parameter selection
        ttk.Label(controls_frame, text="Parameter A:").grid(row=0, column=0, padx=5)
        ttk.Label(controls_frame, text="Parameter B:").grid(row=0, column=2, padx=5)
        
        graph_params = ["Speed Through Water", "Speed Over Ground", "Depth", 
                       "Sea Surface Temperature", "True Wind Speed", "Apparent Wind Speed",
                       "Air Temperature", "Atmospheric Pressure", "Humidity", 
                       "Roll", "Pitch", "Heave"]
        
        combo_a = ttk.Combobox(controls_frame, textvariable=self.graph_param_a, 
                              values=graph_params, state="readonly", width=20)
        combo_a.grid(row=0, column=1, padx=5)
        combo_a.bind("<<ComboboxSelected>>", self.update_graph)
        
        combo_b = ttk.Combobox(controls_frame, textvariable=self.graph_param_b, 
                              values=graph_params, state="readonly", width=20)
        combo_b.grid(row=0, column=3, padx=5)
        combo_b.bind("<<ComboboxSelected>>", self.update_graph)
        
        # Graph canvas
        self.setup_graph_canvas(graph_frame)
    
    def setup_graph_canvas(self, parent):
        """Setup matplotlib canvas for graphs"""
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, parent)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    
    def detect_databases(self):
        """Detect available databases and populate dropdown"""
        detected = self.db_manager.detect_databases()

        # Store mapping of display names to paths
        self.database_paths = {}
        options = []

        if detected:
            for display_name, path in detected:
                options.append(display_name)
                self.database_paths[display_name] = path

        options.append("Select...")

        self.db_combo['values'] = options

        if len(detected) == 1:
            # Auto-select if only one database found
            self.db_combo.current(0)
            self.on_database_selected()
        elif detected:
            # Show first option but don't auto-select
            self.db_combo.current(0)
    
    def on_database_selected(self, event=None):
        """Handle database selection"""
        selection = self.db_var.get()

        if selection == "Select...":
            self.select_database_file()
            return

        # Get path from mapping
        if selection in self.database_paths:
            path = self.database_paths[selection]
            self.load_database(path)
    
    def select_database_file(self):
        """Open file dialog to select database"""
        file_path = filedialog.askopenfilename(
            title="Select OwnShipRecorder Database",
            filetypes=[("TimeZero Database", "*.tzdb"), ("All Files", "*.*")]
        )
        
        if file_path:
            self.load_database(file_path)
    
    def load_database(self, db_path):
        """Load and validate selected database"""
        info = self.db_manager.get_database_info(db_path)
        
        # Display database info
        info_text = f"Database: {db_path}\n"
        info_text += f"Valid: {info['valid']}\n"
        
        if info['error']:
            info_text += f"Status: {info['error']}\n"
        
        if info['valid']:
            info_text += f"Records: {info['record_count']:,}\n"
            
            if info['date_range'][0] and info['date_range'][1]:
                # Convert TimeZero timestamps to readable dates
                extractor = DataExtractor(db_path)
                start_date, end_date = extractor.get_date_range()
                info_text += f"Date Range: {start_date} to {end_date}\n"
        
        self.db_info_text.delete(1.0, tk.END)
        self.db_info_text.insert(1.0, info_text)
        
        if info['valid']:
            self.data_extractor = DataExtractor(db_path)
        else:
            self.data_extractor = None
            messagebox.showerror("Invalid Database", info['error'])
    
    def show_progress(self, message="Loading data..."):
        """Show progress bar with message"""
        self.progress_label.config(text=message)
        self.progress_frame.pack(fill=tk.X, padx=20, pady=10)
        self.progress_bar.start(10)  # Start animation
        self.load_button.config(state='disabled')
        self.root.update()

    def hide_progress(self):
        """Hide progress bar"""
        self.progress_bar.stop()
        self.progress_frame.pack_forget()
        self.load_button.config(state='normal')
        self.root.update()

    def update_progress_message(self, message):
        """Update progress message from background thread"""
        self.root.after(0, lambda: self.progress_label.config(text=message))

    def load_data_thread(self):
        """Load data in background thread"""
        try:
            date_option = self.date_option.get()

            # Create progress callback
            def progress_callback(message):
                self.update_progress_message(message)

            # Update progress message based on selection and load data
            if date_option == "Last day":
                progress_callback("Loading last 24 hours of data...")
                self.current_data = self.data_extractor.get_data_for_timeframe(24, progress_callback)
            elif date_option == "Last 7 days":
                progress_callback("Loading last 7 days of data...")
                self.current_data = self.data_extractor.get_data_for_timeframe(24 * 7, progress_callback)
            elif date_option == "Last month":
                progress_callback("Loading last month of data...")
                self.current_data = self.data_extractor.get_data_for_timeframe(24 * 30, progress_callback)
            else:  # Date Range...
                progress_callback("Loading all available data...")
                self.current_data = self.data_extractor.extract_data(progress_callback=progress_callback)

            # Schedule UI updates on main thread
            self.root.after(0, self.data_loaded_callback)

        except Exception as e:
            # Schedule error handling on main thread
            self.root.after(0, lambda: self.data_load_error(str(e)))

    def data_loaded_callback(self):
        """Called when data loading is complete"""
        self.hide_progress()

        if self.current_data:
            # Update progress for UI operations
            self.show_progress("Updating display...")

            # Use after_idle to allow progress bar to show
            self.root.after_idle(self.update_ui_after_load)
        else:
            messagebox.showwarning("No Data", "No data found for the selected time range")

    def update_ui_after_load(self):
        """Update UI components after data is loaded"""
        try:
            self.refresh_data_display()
            self.update_graph()
            self.hide_progress()

            messagebox.showinfo("Success", f"Loaded {len(self.current_data)} records")

            # Switch to data tab
            self.notebook.select(1)

        except Exception as e:
            self.hide_progress()
            messagebox.showerror("Error", f"Failed to update display: {str(e)}")

    def data_load_error(self, error_message):
        """Called when data loading fails"""
        self.hide_progress()
        messagebox.showerror("Error", f"Failed to load data: {error_message}")

    def load_data(self):
        """Load data based on selected date range"""
        if not self.data_extractor:
            messagebox.showerror("Error", "No valid database selected")
            return

        # Show progress and start loading in background thread
        self.show_progress("Preparing to load data...")

        # Start data loading in background thread
        thread = threading.Thread(target=self.load_data_thread, daemon=True)
        thread.start()
    
    def refresh_data_display(self):
        """Refresh the data table display"""
        if not self.current_data:
            return
        
        # Clear existing data
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)
        
        # Convert to DataFrame
        df = self.data_extractor.to_dataframe(self.current_data, self.use_local_time.get())
        
        # Populate table
        for _, row in df.iterrows():
            values = []
            for col in df.columns:
                value = row[col]
                if pd.isna(value):
                    values.append("")
                elif isinstance(value, float):
                    values.append(f"{value:.2f}")
                else:
                    values.append(str(value))
            
            self.data_tree.insert("", tk.END, values=values)
    
    def update_graph(self, event=None):
        """Update the graph display"""
        if not self.current_data:
            return
        
        try:
            # Clear previous plots
            self.ax1.clear()
            self.ax2.clear()
            
            # Convert to DataFrame for easier plotting
            df = self.data_extractor.to_dataframe(self.current_data, self.use_local_time.get())
            
            if df.empty:
                return
            
            # Convert datetime strings back to datetime objects for plotting
            df['datetime'] = pd.to_datetime(df['Date & Time'])
            
            # Plot parameter A
            param_a = self.graph_param_a.get()
            if param_a in df.columns:
                valid_data_a = df.dropna(subset=[param_a])
                if not valid_data_a.empty:
                    self.ax1.plot(valid_data_a[param_a], valid_data_a['datetime'], 'b-', linewidth=1)
                    self.ax1.set_xlabel(param_a)
                    self.ax1.set_ylabel('Time')
                    self.ax1.set_title(f'{param_a} vs Time')
                    self.ax1.grid(True, alpha=0.3)
            
            # Plot parameter B
            param_b = self.graph_param_b.get()
            if param_b in df.columns:
                valid_data_b = df.dropna(subset=[param_b])
                if not valid_data_b.empty:
                    self.ax2.plot(valid_data_b[param_b], valid_data_b['datetime'], 'r-', linewidth=1)
                    self.ax2.set_xlabel(param_b)
                    self.ax2.set_ylabel('Time')
                    self.ax2.set_title(f'{param_b} vs Time')
                    self.ax2.grid(True, alpha=0.3)
            
            # Adjust layout and refresh
            self.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            print(f"Error updating graph: {e}")
    
    def export_csv(self):
        """Export current data to CSV"""
        if not self.current_data:
            messagebox.showwarning("No Data", "No data to export")
            return
        
        try:
            file_path = filedialog.asksaveasfilename(
                title="Export CSV",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if file_path:
                df = self.data_extractor.to_dataframe(self.current_data, self.use_local_time.get())
                df.to_csv(file_path, index=False)
                messagebox.showinfo("Success", f"Data exported to {file_path}")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to export CSV: {str(e)}")


def main():
    """Main entry point"""
    # Check if command line arguments are provided
    if len(sys.argv) > 1:
        # If arguments provided, use CLI interface
        from cli_interface import main as cli_main
        sys.exit(cli_main())
    else:
        # No arguments, start GUI
        root = tk.Tk()
        app = OwnShipExtractorGUI(root)
        root.mainloop()


if __name__ == "__main__":
    main()
