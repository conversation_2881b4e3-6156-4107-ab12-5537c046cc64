apiVersion: v1
kind: ConfigMap
metadata:
  name: call-analysis-config
  labels:
    app: call-analysis
data:
  # Flask configuration
  FLASK_ENV: "production"
  FLASK_APP: "app.py"
  
  # Application settings
  MAX_CONTENT_LENGTH: "********"  # 16MB
  DATABASE_PATH: "/app/data/call_analysis.db"
  REPORTS_PATH: "/app/reports"
  
  # RingCX API settings
  RINGCX_BASE_URL: "https://ringcx.ringcentral.com/voice"
  REPORTS_STREAMING_ENDPOINT: "/api/v1/admin/accounts/********/reportsStreaming"
  
  # Default query settings
  DEFAULT_DAYS: "7"
  MAX_DAYS: "30"
  
  # Gunicorn settings
  GUNICORN_WORKERS: "2"
  GUNICORN_TIMEOUT: "120"
  GUNICORN_BIND: "0.0.0.0:5000"
