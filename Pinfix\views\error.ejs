<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i>
                PinFix
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 4rem;"></i>
                    </div>

                    <h1 class="display-6 mb-3"><%= message %></h1>

                    <% if (error && error.stack && process.env.NODE_ENV === 'development') { %>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Error Details (Development Mode)</h5>
                            </div>
                            <div class="card-body">
                                <pre class="text-start"><%= error.stack %></pre>
                            </div>
                        </div>
                    <% } %>

                    <div class="mt-4">
                        <a href="/" class="btn btn-primary">
                            <i class="bi bi-house"></i> Go Home
                        </a>
                        <button id="back-button" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Error JS -->
    <script src="/static/js/error.js"></script>
</body>
</html>
