#!/bin/bash

# Call Analysis Deployment Script
# Usage: ./deploy.sh [docker|k8s|clean]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="call-analysis"
NAMESPACE="call-analysis"
IMAGE_NAME="call-analysis:latest"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required files exist
    if [[ ! -f "Assembly_KEY.txt" ]]; then
        log_error "Assembly_KEY.txt not found. Please create this file with your AssemblyAI API key."
        exit 1
    fi
    
    if [[ ! -f "OpenAI_KEY.txt" ]]; then
        log_error "OpenAI_KEY.txt not found. Please create this file with your OpenAI API key."
        exit 1
    fi
    
    if [[ ! -f "rc-credentials.json" ]]; then
        log_error "rc-credentials.json not found. Please copy your RingCentral credentials file."
        exit 1
    fi
    
    log_success "All required files found."
}

deploy_docker() {
    log_info "Deploying with Docker Compose..."
    
    check_prerequisites
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose not found. Please install docker-compose."
        exit 1
    fi
    
    # Build and start services
    log_info "Building Docker image..."
    docker-compose build
    
    log_info "Starting services..."
    docker-compose up -d
    
    # Wait for service to be ready
    log_info "Waiting for service to be ready..."
    sleep 10
    
    # Check if service is running
    if docker-compose ps | grep -q "Up"; then
        log_success "Application deployed successfully!"
        log_info "Access the application at: http://localhost:5000"
        log_info "Check logs with: docker-compose logs -f"
    else
        log_error "Deployment failed. Check logs with: docker-compose logs"
        exit 1
    fi
}

deploy_k8s() {
    log_info "Deploying to Kubernetes..."
    
    check_prerequisites
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl not found. Please install kubectl."
        exit 1
    fi
    
    # Check if cluster is accessible
    if ! kubectl cluster-info > /dev/null 2>&1; then
        log_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
        exit 1
    fi
    
    # Create namespace if it doesn't exist
    log_info "Creating namespace..."
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Create secrets
    log_info "Creating secrets..."
    kubectl create secret generic call-analysis-secrets \
        --from-file=ASSEMBLYAI_API_KEY=Assembly_KEY.txt \
        --from-file=OPENAI_API_KEY=OpenAI_KEY.txt \
        --from-file=RC_CREDENTIALS_JSON=rc-credentials.json \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Build Docker image (assuming local registry or you'll push to remote)
    log_info "Building Docker image..."
    docker build -t $IMAGE_NAME .
    
    # Apply Kubernetes manifests
    log_info "Applying Kubernetes manifests..."
    kubectl apply -f k8s/ --namespace=$NAMESPACE
    
    # Wait for deployment to be ready
    log_info "Waiting for deployment to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/$APP_NAME -n $NAMESPACE
    
    # Get service information
    log_success "Application deployed successfully!"
    log_info "Deployment status:"
    kubectl get pods,services -n $NAMESPACE
    
    log_info "To access the application:"
    log_info "1. Port forward: kubectl port-forward service/call-analysis-service 5000:80 -n $NAMESPACE"
    log_info "2. Then access: http://localhost:5000"
}

clean_deployment() {
    log_info "Cleaning up deployment..."
    
    # Ask for confirmation
    read -p "Are you sure you want to clean up the deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cleanup cancelled."
        exit 0
    fi
    
    # Clean Docker Compose
    if command -v docker-compose &> /dev/null && [[ -f "docker-compose.yml" ]]; then
        log_info "Stopping Docker Compose services..."
        docker-compose down -v
        
        # Remove images
        read -p "Remove Docker images? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down --rmi all -v
        fi
    fi
    
    # Clean Kubernetes
    if command -v kubectl &> /dev/null; then
        if kubectl get namespace $NAMESPACE > /dev/null 2>&1; then
            log_info "Cleaning up Kubernetes resources..."
            kubectl delete namespace $NAMESPACE
        fi
    fi
    
    log_success "Cleanup completed!"
}

show_help() {
    echo "Call Analysis Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  docker    Deploy using Docker Compose"
    echo "  k8s       Deploy to Kubernetes"
    echo "  clean     Clean up all deployments"
    echo "  help      Show this help message"
    echo ""
    echo "Prerequisites:"
    echo "  - Assembly_KEY.txt (AssemblyAI API key)"
    echo "  - OpenAI_KEY.txt (OpenAI API key)"
    echo "  - rc-credentials.json (RingCentral credentials)"
    echo ""
    echo "Examples:"
    echo "  $0 docker     # Deploy with Docker Compose"
    echo "  $0 k8s        # Deploy to Kubernetes"
    echo "  $0 clean      # Clean up all deployments"
}

# Main script logic
case "${1:-help}" in
    docker)
        deploy_docker
        ;;
    k8s)
        deploy_k8s
        ;;
    clean)
        clean_deployment
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
