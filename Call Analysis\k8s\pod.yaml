apiVersion: v1
kind: Pod
metadata:
  name: call-analysis-pod
  labels:
    app: call-analysis
    version: v1.0
spec:
  containers:
  - name: call-analysis
    image: call-analysis:latest
    imagePullPolicy: IfNotPresent
    
    ports:
    - containerPort: 5000
      name: http
      protocol: TCP
    
    env:
    # Flask configuration
    - name: FLASK_ENV
      value: "production"
    - name: FLASK_APP
      value: "app.py"
    - name: PYTHONUNBUFFERED
      value: "1"
    
    # Application configuration
    - name: DATABASE_PATH
      value: "/app/data/call_analysis.db"
    - name: REPORTS_PATH
      value: "/app/reports"
    
    # API Keys from secrets
    - name: ASSEMBLYAI_API_KEY
      valueFrom:
        secretKeyRef:
          name: call-analysis-secrets
          key: ASSEMBLYAI_API_KEY
    - name: OPENAI_API_KEY
      valueFrom:
        secretKeyRef:
          name: call-analysis-secrets
          key: OPENAI_API_KEY
    
    # RingCX configuration
    - name: RINGCX_BASE_URL
      value: "https://ringcx.ringcentral.com/voice"
    
    volumeMounts:
    - name: data-storage
      mountPath: /app/data
    - name: reports-storage
      mountPath: /app/reports
    - name: api-keys-volume
      mountPath: /app/Assembly_KEY.txt
      subPath: ASSEMBLYAI_API_KEY
      readOnly: true
    - name: api-keys-volume
      mountPath: /app/OpenAI_KEY.txt
      subPath: OPENAI_API_KEY
      readOnly: true
    - name: rc-credentials-volume
      mountPath: /app/rc-credentials.json
      subPath: RC_CREDENTIALS_JSON
      readOnly: true
    
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
    
    # Health checks
    livenessProbe:
      httpGet:
        path: /health
        port: 5000
        scheme: HTTP
      initialDelaySeconds: 30
      periodSeconds: 30
      timeoutSeconds: 10
      successThreshold: 1
      failureThreshold: 3
    
    readinessProbe:
      httpGet:
        path: /health
        port: 5000
        scheme: HTTP
      initialDelaySeconds: 5
      periodSeconds: 10
      timeoutSeconds: 5
      successThreshold: 1
      failureThreshold: 3
    
    # Security context
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
      runAsUser: 1000
      runAsGroup: 1000
      readOnlyRootFilesystem: false
      capabilities:
        drop:
        - ALL
  
  volumes:
  - name: data-storage
    persistentVolumeClaim:
      claimName: call-analysis-data
  - name: reports-storage
    persistentVolumeClaim:
      claimName: call-analysis-reports
  - name: api-keys-volume
    secret:
      secretName: call-analysis-secrets
      items:
      - key: ASSEMBLYAI_API_KEY
        path: ASSEMBLYAI_API_KEY
      - key: OPENAI_API_KEY
        path: OPENAI_API_KEY
  - name: rc-credentials-volume
    secret:
      secretName: call-analysis-secrets
      items:
      - key: RC_CREDENTIALS_JSON
        path: RC_CREDENTIALS_JSON
  
  # Pod-level security context
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
  
  # Restart policy
  restartPolicy: Always
  
  # DNS policy
  dnsPolicy: ClusterFirst
  
  # Termination grace period
  terminationGracePeriodSeconds: 30
