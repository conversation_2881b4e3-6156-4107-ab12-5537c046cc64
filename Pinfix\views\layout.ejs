<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    <% if (typeof additionalCSS !== 'undefined') { %>
        <% additionalCSS.forEach(css => { %>
            <link href="<%= css %>" rel="stylesheet">
        <% }); %>
    <% } %>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                PinFix
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <% if (typeof operatorContext !== 'undefined' && operatorContext) { %>
                        <li class="nav-item">
                            <span class="navbar-text me-3">
                                <%= operatorContext.company_name || operatorContext.account_name %>
                            </span>
                        </li>
                    <% } %>
                    
                    <% if (typeof session !== 'undefined' && session) { %>
                        <% if (session.superAdmin) { %>
                            <li class="nav-item">
                                <a class="nav-link" href="/admin">Super Admin</a>
                            </li>
                            <li class="nav-item">
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="btn btn-link nav-link border-0">Logout</button>
                                </form>
                            </li>
                        <% } else if (session.operator) { %>
                            <li class="nav-item">
                                <a class="nav-link" href="/<%= session.operator.account_name %>/admin">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/<%= session.operator.account_name %>/issues">Issues</a>
                            </li>
                            <li class="nav-item">
                                <form method="POST" action="/<%= session.operator.account_name %>/logout" class="d-inline">
                                    <button type="submit" class="btn btn-link nav-link border-0">Logout</button>
                                </form>
                            </li>
                        <% } else if (session.tech) { %>
                            <li class="nav-item">
                                <a class="nav-link" href="/<%= session.tech.operator_account %>/issues">My Issues</a>
                            </li>
                            <li class="nav-item">
                                <form method="POST" action="/<%= session.tech.operator_account %>/logout" class="d-inline">
                                    <button type="submit" class="btn btn-link nav-link border-0">Logout</button>
                                </form>
                            </li>
                        <% } %>
                    <% } %>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
        
        <% if (typeof success !== 'undefined' && success) { %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
        
        <%- body %>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    
    <% if (typeof additionalJS !== 'undefined') { %>
        <% additionalJS.forEach(js => { %>
            <script src="<%= js %>"></script>
        <% }); %>
    <% } %>
</body>
</html>
