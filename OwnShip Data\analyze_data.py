import sqlite3
import datetime

def analyze_data():
    conn = sqlite3.connect('OwnShipRecorder.tzdb')
    cursor = conn.cursor()
    
    # Check date range
    cursor.execute("SELECT MIN(Date), MAX(Date) FROM Data")
    min_date, max_date = cursor.fetchone()
    print(f"Date range (raw): {min_date} to {max_date}")
    
    # Convert dates - TimeZero uses milliseconds since 2000-01-01
    tz_epoch = datetime.datetime(2000, 1, 1)
    
    if min_date:
        min_datetime = tz_epoch + datetime.timedelta(milliseconds=min_date)
        max_datetime = tz_epoch + datetime.timedelta(milliseconds=max_date)
        print(f"Date range (converted): {min_datetime} to {max_datetime}")
    
    # Check data scaling - values seem to be scaled integers
    cursor.execute("SELECT Date, X, Y, Depth, CourseOverGround, SpeedOverGround, Heading FROM Data LIMIT 5")
    rows = cursor.fetchall()
    
    print("\nSample data with conversions:")
    for row in rows:
        date_ms, x, y, depth, cog, sog, heading = row
        dt = tz_epoch + datetime.timedelta(milliseconds=date_ms)
        
        # Convert coordinates (likely scaled by 10^7 for degrees)
        lat = y / 10000000.0 if y != 0 else None
        lon = x / 10000000.0 if x != 0 else None
        
        # Convert other values (need to determine scaling)
        depth_m = depth / 100.0 if depth != 0 else None  # likely cm to m
        cog_deg = cog / 100.0 if cog != 0 else None      # likely 0.01 degree precision
        sog_kn = sog / 100.0 if sog != 0 else None       # likely 0.01 knot precision
        heading_deg = heading / 100.0 if heading != 0 else None
        
        print(f"  {dt}: Lat={lat}, Lon={lon}, Depth={depth_m}m, COG={cog_deg}°, SOG={sog_kn}kn, HDG={heading_deg}°")
    
    # Check data availability
    cursor.execute("SELECT * FROM DataAvailability")
    availability = cursor.fetchall()
    print(f"\nData availability: {availability}")
    
    conn.close()

if __name__ == "__main__":
    analyze_data()
