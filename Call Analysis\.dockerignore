# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
DEPLOYMENT.md
*.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database files (will be created in container)
*.db
*.sqlite
*.sqlite3

# Generated reports
*.pdf
call_analysis_report_*.pdf

# CSV files (data files)
*.csv
ringcx_call_report_*.csv

# Build artifacts
build/
dist/
*.spec

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Kubernetes
k8s/
*.yaml
*.yml

# Scripts
deploy.sh
build_exe.bat
install_dependencies.py

# Test files
test_*.py
temp.py
example_usage.py

# Backup files
*.bak
*.backup

# Temporary files
tmp/
temp/
.tmp/
