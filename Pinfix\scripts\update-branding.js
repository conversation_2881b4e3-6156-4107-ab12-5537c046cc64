const fs = require('fs');
const path = require('path');

// Function to recursively find all .ejs files
function findEjsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      findEjsFiles(filePath, fileList);
    } else if (file.endsWith('.ejs')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to update branding in a file
function updateBranding(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;
    
    // Replace title tags
    if (content.includes('| Pinfix Repair</title>')) {
      content = content.replace(/\| Pinfix Repair<\/title>/g, '| PinFix</title>');
      updated = true;
    }
    
    // Replace navbar brand
    if (content.includes('Pinfix Repair')) {
      content = content.replace(/Pinfix Repair/g, 'PinFix');
      updated = true;
    }
    
    // Replace any other instances
    if (content.includes('Powered by Pinfix Repair')) {
      content = content.replace(/Powered by Pinfix Repair/g, 'Powered by PinFix');
      updated = true;
    }
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Updated: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error.message);
  }
}

// Main execution
console.log('Updating branding from "Pinfix Repair" to "PinFix"...');

const viewsDir = path.join(__dirname, '..', 'views');
const ejsFiles = findEjsFiles(viewsDir);

console.log(`Found ${ejsFiles.length} EJS files to check`);

ejsFiles.forEach(updateBranding);

console.log('Branding update completed!');
