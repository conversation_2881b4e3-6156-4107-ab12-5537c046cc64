<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Success/Error Messages -->
        <% if (success) { %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
        <% if (error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-people-fill"></i> Manage Techs</h1>
                    <a href="/<%= operator.username %>/admin/techs/new" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Add New Tech
                    </a>
                </div>
            </div>
        </div>

        <!-- Techs List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">All Techs</h5>
                    </div>
                    <div class="card-body">
                        <% if (techs.length === 0) { %>
                            <div class="text-center py-5">
                                <i class="bi bi-people display-1 text-muted"></i>
                                <h4 class="text-muted mt-3">No Techs Added Yet</h4>
                                <p class="text-muted">Add your first technician to start assigning issues to other people.</p>
                                <a href="/<%= operator.username %>/admin/techs/new" class="btn btn-primary">
                                    <i class="bi bi-person-plus"></i> Add Your First Tech
                                </a>
                            </div>
                        <% } else { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Username</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% techs.forEach(tech => { %>
                                            <tr>
                                                <td>
                                                    <strong><%= tech.name %></strong>
                                                </td>
                                                <td>
                                                    <a href="mailto:<%= tech.email %>"><%= tech.email %></a>
                                                </td>
                                                <td>
                                                    <code><%= tech.username %></code>
                                                </td>
                                                <td>
                                                    <% if (tech.status === 'active') { %>
                                                        <span class="badge bg-success">Active</span>
                                                    <% } else { %>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <%= new Date(tech.created_at).toLocaleDateString() %>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="/<%= operator.username %>/admin/techs/<%= tech.id %>/edit" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="bi bi-pencil"></i> Edit
                                                        </a>
                                                        <% if (tech.status === 'active') { %>
                                                            <form method="POST" action="/<%= operator.username %>/admin/techs/<%= tech.id %>/deactivate" class="d-inline">
                                                                <button type="submit" class="btn btn-sm btn-outline-warning" 
                                                                        onclick="return confirm('Are you sure you want to deactivate this tech?')">
                                                                    <i class="bi bi-pause-circle"></i> Deactivate
                                                                </button>
                                                            </form>
                                                        <% } else { %>
                                                            <form method="POST" action="/<%= operator.username %>/admin/techs/<%= tech.id %>/reactivate" class="d-inline">
                                                                <button type="submit" class="btn btn-sm btn-outline-success" 
                                                                        onclick="return confirm('Are you sure you want to reactivate this tech?')">
                                                                    <i class="bi bi-play-circle"></i> Reactivate
                                                                </button>
                                                            </form>
                                                        <% } %>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
</body>
</html>
