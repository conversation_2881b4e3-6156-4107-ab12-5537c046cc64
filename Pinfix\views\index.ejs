<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/auth/login">Operator Login</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-5">
                    <h1 class="display-4 mb-4">
                        <i class="bi bi-tools text-primary"></i>
                        PinFix
                    </h1>
                    <p class="lead">Never lose track of a pinball problem again — simple, powerful issue management for operators.</p>
                    <p class="fst-italic text-muted mb-4">Keep your machines flipping, not flopping</p>
                </div>

                <!-- How It Works Section -->
                <div class="row mb-5">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title text-center mb-4">How It Works</h5>
                                <div class="row">
                                    <div class="col-md-4 text-center mb-4">
                                        <i class="bi bi-1-circle-fill text-primary fs-1"></i>
                                        <h6 class="mt-2">Add Machine</h6>
                                        <p class="small">Add a pinball machine; we auto-generate a unique QR code.</p>
                                    </div>
                                    <div class="col-md-4 text-center mb-4">
                                        <i class="bi bi-2-circle-fill text-primary fs-1"></i>
                                        <h6 class="mt-2">Print & Place</h6>
                                        <p class="small">Download, print, and stick the QR code on the machine.</p>
                                    </div>
                                    <div class="col-md-4 text-center mb-4">
                                        <i class="bi bi-3-circle-fill text-primary fs-1"></i>
                                        <h6 class="mt-2">Scan to Report</h6>
                                        <p class="small">Players scan the QR, pick an issue type, add notes, and snap a photo.</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4 text-center mb-3">
                                        <i class="bi bi-4-circle-fill text-primary fs-1"></i>
                                        <h6 class="mt-2">Instant Alerts</h6>
                                        <p class="small">You're notified immediately when a new issue is submitted.</p>
                                    </div>
                                    <div class="col-md-4 text-center mb-3">
                                        <i class="bi bi-5-circle-fill text-primary fs-1"></i>
                                        <h6 class="mt-2">Assign & Fix</h6>
                                        <p class="small">Handle it yourself or dispatch a tech; add updates as you go.</p>
                                    </div>
                                    <div class="col-md-4 text-center mb-3">
                                        <i class="bi bi-6-circle-fill text-primary fs-1"></i>
                                        <h6 class="mt-2">Track Status</h6>
                                        <p class="small">Move issues through Open → In Progress → Assigned → Resolved to track the lifecycle.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Operator Login Section -->
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="bi bi-buildings-fill display-1 text-success mb-3"></i>
                                <h5 class="card-title">Operator Login</h5>
                                <p class="card-text">Arcade operators can manage their pinball machines and track issues.</p>
                                <a href="/auth/login" class="btn btn-success btn-lg">Operator Login</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0"><i class="bi bi-tools"></i> Powered by PinFix</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
</body>
</html>
