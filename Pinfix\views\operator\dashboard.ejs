<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-speedometer2"></i> Dashboard</h1>
                    <div>
                        <span class="badge bg-success">
                            <i class="bi bi-buildings"></i> <%= operator.name %>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error/Success Messages -->
        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Statistics Cards -->
        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Pinball Machines</h6>
                                <div class="stats-number"><%= stats.machines %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-controller fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Open Issues</h6>
                                <div class="stats-number"><%= stats.openIssues %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-exclamation-circle fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Total Issues</h6>
                                <div class="stats-number"><%= stats.issues %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-exclamation-triangle fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Active Techs</h6>
                                <div class="stats-number"><%= stats.techs %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-people fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="/<%= operator.username %>/admin/machines" class="btn btn-primary w-100">
                                    <i class="bi bi-controller"></i> Manage Machines
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/<%= operator.username %>/admin/techs" class="btn btn-success w-100">
                                    <i class="bi bi-people"></i> Manage Techs
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/<%= operator.username %>/admin/issues" class="btn btn-warning w-100">
                                    <i class="bi bi-list-check"></i> Manage Issues
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/<%= operator.username %>/admin/reports" class="btn btn-info w-100">
                                    <i class="bi bi-graph-up"></i> View Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Issues</h5>
                    </div>
                    <div class="card-body">
                        <% if (recentIssues.length === 0) { %>
                            <div class="text-center py-4">
                                <i class="bi bi-check-circle display-4 text-success"></i>
                                <h6 class="text-muted mt-3">No Recent Issues</h6>
                                <p class="text-muted">Great job! No issues have been reported recently.</p>
                            </div>
                        <% } else { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Machine</th>
                                            <th>Issue Type</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Reported</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% recentIssues.forEach(issue => { %>
                                            <tr>
                                                <td>
                                                    <%= issue.machine_name %>
                                                    <% if (issue.location_notes) { %>
                                                        <br><small class="text-muted">
                                                            <i class="bi bi-geo-alt"></i> <%= issue.location_notes %>
                                                        </small>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <%= issue.issue_type %>
                                                    <% if (issue.user_comment) { %>
                                                        <br><small class="text-muted"><%= issue.user_comment.substring(0, 50) %><%= issue.user_comment.length > 50 ? '...' : '' %></small>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <%
                                                    let priorityClass = 'bg-secondary';
                                                    if (issue.priority === 'High') priorityClass = 'bg-danger';
                                                    else if (issue.priority === 'Medium') priorityClass = 'bg-warning';
                                                    else if (issue.priority === 'Low') priorityClass = 'bg-success';
                                                    %>
                                                    <span class="badge <%= priorityClass %>"><%= issue.priority %></span>
                                                </td>
                                                <td>
                                                    <%
                                                    let statusClass = 'bg-secondary';
                                                    if (issue.status === 'Open') statusClass = 'bg-danger';
                                                    else if (issue.status === 'Resolved') statusClass = 'bg-success';
                                                    else if (issue.status === 'Assigned' || issue.status === 'Not Duplicated') statusClass = 'bg-warning';
                                                    %>
                                                    <span class="badge <%= statusClass %>"><%= issue.status %></span>
                                                </td>
                                                <td>
                                                    <small>
                                                        <%= issue.formatted_created_date %><br>
                                                        <span class="text-muted"><%= issue.formatted_created_time %></span>
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="/<%= operator.username %>/admin/issues/<%= issue.id %>"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="bi bi-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
</body>
</html>
