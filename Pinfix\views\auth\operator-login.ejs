<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                 <i class="bi bi-tools"></i> PinFix
            </a>
        </div>
    </nav>

    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100">
            <div class="col-md-6 col-lg-4 mx-auto">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <!-- Logo/Header -->
                        <div class="text-center mb-4">
                            <h1 class="h3 mb-3">
                                <i class="bi bi-tools text-primary"></i>
                                PinFix
                            </h1>
                            <h4><i class="bi bi-shield-lock"></i> Admin Login</h4>
                            <% if (operator && operator.name) { %>
                                <p class="text-muted">
                                    <i class="bi bi-buildings"></i> <%= operator.name %>
                                </p>
                            <% } %>
                        </div>

                        <!-- Error Message -->
                        <% if (error) { %>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle"></i>
                                <%= error %>
                            </div>
                        <% } %>

                        <!-- Login Form -->
                        <form method="POST">
                            <input type="hidden" name="userType" value="operator">

                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    <input type="text"
                                           class="form-control"
                                           id="username"
                                           name="username"
                                           placeholder="Enter your username"
                                           required>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password"
                                           class="form-control"
                                           id="password"
                                           name="password"
                                           placeholder="Enter your password"
                                           required>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right"></i> Login as Admin
                                </button>
                            </div>
                        </form>

                        <!-- Tech Login Link -->
                        <div class="text-center mt-3">
                            <% if (operator && operator.username) { %>
                                <small class="text-muted">
                                    Are you a technician?
                                    <a href="/<%= operator.username %>/tech/login" class="text-decoration-none">
                                        Click here to login as Technician
                                    </a>
                                </small>
                            <% } %>
                        </div>


                    </div>
                </div>

                <!-- Additional Info -->
                <div class="text-center mt-3">
                    <small class="text-muted">
                        Administrator access for <%= operator.name %>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
</body>
</html>
