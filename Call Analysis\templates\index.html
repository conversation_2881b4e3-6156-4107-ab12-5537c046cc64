<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Call Analysis Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .controls-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        input, select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .calls-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .progress-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .results {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }
        
        .checkbox-column {
            width: 50px;
            text-align: center;
        }
        
        .url-column {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📞 Call Analysis Dashboard</h1>
            <p>Analyze call recordings with AI-powered sentiment analysis</p>
        </div>
        
        <div class="controls">
            <div class="controls-row">
                <label>Query last:</label>
                <input type="number" id="daysInput" value="7" min="1" max="30" style="width: 80px;">
                <label>days</label>
                <button class="btn" onclick="loadCalls()">🔄 Refresh Data</button>
                <button class="btn btn-success" onclick="selectAll()">✓ Select All</button>
                <button class="btn btn-danger" onclick="deselectAll()">✗ Deselect All</button>
                <button class="btn" onclick="analyzeSelected()" id="analyzeBtn" disabled>🔍 Analyze Selected</button>
            </div>
        </div>
        
        <div class="status" id="statusDiv">
            <span id="statusText">Ready to load call data</span>
        </div>
        
        <div class="progress-container" id="progressContainer">
            <h3>Analysis Progress</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText">Initializing...</p>
        </div>
        
        <div class="calls-table" id="callsTable" style="display: none;">
            <table>
                <thead>
                    <tr>
                        <th class="checkbox-column">Select</th>
                        <th>Agent Name</th>
                        <th>Phone Number</th>
                        <th>Date/Time</th>
                        <th>Duration (sec)</th>
                        <th>Queue</th>
                        <th class="url-column">Recording URL</th>
                    </tr>
                </thead>
                <tbody id="callsTableBody">
                </tbody>
            </table>
        </div>
        
        <div class="results" id="resultsDiv">
            <h3>Analysis Results</h3>
            <div id="resultsContent"></div>
            <button class="btn" onclick="generateReport()" id="generateReportBtn">📄 Generate PDF Report</button>
            <button class="btn" onclick="viewDatabase()">📊 View Database Records</button>
        </div>
    </div>

    <script>
        let callsData = [];
        let analysisResults = [];
        
        // Load calls on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkConfig();
        });
        
        async function checkConfig() {
            try {
                const response = await fetch('/api/config');
                const config = await response.json();
                
                if (!config.getreport_available) {
                    showStatus('Warning: getReport module not available. Manual CSV upload not yet implemented.', 'alert-error');
                } else {
                    loadCalls();
                }
            } catch (error) {
                showStatus('Error checking configuration: ' + error.message, 'alert-error');
            }
        }
        
        async function loadCalls() {
            const days = document.getElementById('daysInput').value;
            showStatus('Loading call data...', 'alert-info');
            
            try {
                const response = await fetch(`/api/calls?days=${days}`);
                const data = await response.json();
                
                if (response.ok) {
                    callsData = data.calls;
                    displayCalls(callsData);
                    showStatus(`Loaded ${data.total} calls from last ${data.days} days`, 'alert-success');
                } else {
                    showStatus('Error: ' + data.error, 'alert-error');
                }
            } catch (error) {
                showStatus('Error loading calls: ' + error.message, 'alert-error');
            }
        }
        
        function displayCalls(calls) {
            const tableBody = document.getElementById('callsTableBody');
            const table = document.getElementById('callsTable');
            
            tableBody.innerHTML = '';
            
            calls.forEach((call, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="checkbox-column">
                        <input type="checkbox" id="call_${index}" onchange="updateAnalyzeButton()">
                    </td>
                    <td>${call.agent_name}</td>
                    <td>${call.ani}</td>
                    <td>${call.connected_dts}</td>
                    <td>${call.connected_duration}</td>
                    <td>${call.source_name}</td>
                    <td class="url-column" title="${call.recording_url}">${call.recording_url}</td>
                `;
                tableBody.appendChild(row);
            });
            
            table.style.display = 'block';
            updateAnalyzeButton();
        }
        
        function selectAll() {
            const checkboxes = document.querySelectorAll('#callsTableBody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateAnalyzeButton();
        }
        
        function deselectAll() {
            const checkboxes = document.querySelectorAll('#callsTableBody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateAnalyzeButton();
        }
        
        function updateAnalyzeButton() {
            const checkedBoxes = document.querySelectorAll('#callsTableBody input[type="checkbox"]:checked');
            const analyzeBtn = document.getElementById('analyzeBtn');
            analyzeBtn.disabled = checkedBoxes.length === 0;
        }
        
        async function analyzeSelected() {
            const checkedBoxes = document.querySelectorAll('#callsTableBody input[type="checkbox"]:checked');
            const selectedCalls = [];
            
            checkedBoxes.forEach(cb => {
                const index = parseInt(cb.id.split('_')[1]);
                selectedCalls.push(callsData[index]);
            });
            
            if (selectedCalls.length === 0) {
                showStatus('No calls selected', 'alert-error');
                return;
            }
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ calls: selectedCalls })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showStatus(`Analysis started for ${data.total_calls} calls`, 'alert-info');
                    startProgressMonitoring();
                } else {
                    showStatus('Error: ' + data.error, 'alert-error');
                }
            } catch (error) {
                showStatus('Error starting analysis: ' + error.message, 'alert-error');
            }
        }
        
        function startProgressMonitoring() {
            const progressContainer = document.getElementById('progressContainer');
            progressContainer.style.display = 'block';
            
            const interval = setInterval(async () => {
                try {
                    const response = await fetch('/api/progress');
                    const progress = await response.json();
                    
                    updateProgress(progress);
                    
                    if (progress.status === 'completed' || progress.status === 'error') {
                        clearInterval(interval);
                        if (progress.status === 'completed') {
                            showResults(progress.results);
                        }
                    }
                } catch (error) {
                    console.error('Error checking progress:', error);
                    clearInterval(interval);
                }
            }, 1000);
        }
        
        function updateProgress(progress) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            const percentage = progress.total > 0 ? (progress.current / progress.total) * 100 : 0;
            progressFill.style.width = percentage + '%';
            progressText.textContent = `${progress.message} (${progress.current}/${progress.total})`;
        }
        
        function showResults(results) {
            analysisResults = results;
            const resultsDiv = document.getElementById('resultsDiv');
            const resultsContent = document.getElementById('resultsContent');
            
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    Analysis completed successfully! Processed ${results.length} calls.
                    Results have been saved to the database.
                </div>
            `;
            
            resultsDiv.style.display = 'block';
        }
        
        async function generateReport() {
            try {
                const response = await fetch('/api/reports/generate', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showStatus(`PDF report generated: ${data.filename}`, 'alert-success');
                    
                    // Create download link
                    const downloadLink = document.createElement('a');
                    downloadLink.href = `/api/reports/download/${data.filename}`;
                    downloadLink.download = data.filename;
                    downloadLink.textContent = `📥 Download ${data.filename}`;
                    downloadLink.className = 'btn';
                    downloadLink.style.marginLeft = '10px';
                    
                    const generateBtn = document.getElementById('generateReportBtn');
                    generateBtn.parentNode.insertBefore(downloadLink, generateBtn.nextSibling);
                } else {
                    showStatus('Error generating report: ' + data.error, 'alert-error');
                }
            } catch (error) {
                showStatus('Error generating report: ' + error.message, 'alert-error');
            }
        }
        
        async function viewDatabase() {
            try {
                const response = await fetch('/api/database/records?limit=20');
                const data = await response.json();
                
                if (response.ok) {
                    displayDatabaseRecords(data.records);
                } else {
                    showStatus('Error loading database records: ' + data.error, 'alert-error');
                }
            } catch (error) {
                showStatus('Error loading database records: ' + error.message, 'alert-error');
            }
        }
        
        function displayDatabaseRecords(records) {
            const resultsContent = document.getElementById('resultsContent');
            
            let html = '<h4>Recent Database Records</h4>';
            html += '<table style="width: 100%; margin-top: 15px;">';
            html += '<thead><tr><th>ID</th><th>Date/Time</th><th>Agent</th><th>Sentiment</th><th>Queue</th><th>Phone</th></tr></thead>';
            html += '<tbody>';
            
            records.forEach(record => {
                const sentimentColor = record.sentiment_score > 0 ? 'green' : record.sentiment_score < 0 ? 'red' : 'black';
                html += `
                    <tr>
                        <td>${record.id}</td>
                        <td>${record.date_time}</td>
                        <td>${record.agent_name}</td>
                        <td style="color: ${sentimentColor}; font-weight: bold;">${record.sentiment_score.toFixed(2)}</td>
                        <td>${record.source_name || 'N/A'}</td>
                        <td>${record.ani || 'N/A'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            resultsContent.innerHTML = html;
        }
        
        function showStatus(message, alertClass = 'alert-info') {
            const statusDiv = document.getElementById('statusDiv');
            statusDiv.innerHTML = `<div class="${alertClass}">${message}</div>`;
        }
    </script>
</body>
</html>
