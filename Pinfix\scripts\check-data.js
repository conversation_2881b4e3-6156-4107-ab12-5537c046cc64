const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'pinfix.db');

// Open database
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
    process.exit(1);
  }
  console.log('Connected to SQLite database');
});

// Check data in tables
db.serialize(() => {
  console.log('=== OPERATORS ===');
  db.all("SELECT * FROM operators", (err, rows) => {
    if (err) {
      console.error('Error getting operators:', err.message);
    } else {
      console.log('Operators count:', rows.length);
      rows.forEach(row => {
        console.log(`- ID: ${row.id}, Username: ${row.username}, Name: ${row.name}`);
      });
    }
  });

  console.log('\n=== GLOBAL PINBALL MACHINES ===');
  db.all("SELECT * FROM global_pinball_machines", (err, rows) => {
    if (err) {
      console.error('Error getting global machines:', err.message);
    } else {
      console.log('Global machines count:', rows.length);
      rows.forEach(row => {
        console.log(`- ID: ${row.id}, Name: ${row.name}, Manufacturer: ${row.manufacturer}`);
      });
    }
  });

  console.log('\n=== PINBALL MACHINES ===');
  db.all("SELECT * FROM pinball_machines", (err, rows) => {
    if (err) {
      console.error('Error getting pinball machines:', err.message);
    } else {
      console.log('Pinball machines count:', rows.length);
      rows.forEach(row => {
        console.log(`- ID: ${row.id}, Operator ID: ${row.operator_id}, Global Machine ID: ${row.global_machine_id}, Status: ${row.status}`);
      });
    }
  });

  console.log('\n=== ISSUES ===');
  db.all("SELECT * FROM issues", (err, rows) => {
    if (err) {
      console.error('Error getting issues:', err.message);
    } else {
      console.log('Issues count:', rows.length);
      rows.forEach(row => {
        console.log(`- ID: ${row.id}, Machine ID: ${row.machine_id}, Issue Type: ${row.issue_type}, Priority: ${row.priority}, Status: ${row.status}`);
      });
    }
    
    // Close database
    setTimeout(() => {
      db.close((err) => {
        if (err) {
          console.error('Error closing database:', err.message);
        } else {
          console.log('\nDatabase check completed');
        }
      });
    }, 1000);
  });
});
