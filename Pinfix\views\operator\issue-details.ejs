<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-bug"></i> Issue #<%= issue.id %>
                    </h1>
                    <div>
                        <a href="/<%= operator.username %>/admin/machines/<%= issue.machine_id %>" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-controller"></i> View Machine
                        </a>
                        <a href="/<%= operator.username %>/admin/issues" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Issues
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <% if (typeof success !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%= success %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Issue Details -->
        <div class="row">
            <!-- Main Issue Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Issue Details</h5>
                    </div>
                    <div class="card-body">
                        <!-- Issue Type and Priority -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6 class="text-muted">Issue Type</h6>
                                <h4><%= issue.issue_type %></h4>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Priority</h6>
                                <% 
                                let priorityClass = 'bg-secondary';
                                if (issue.priority === 'High') priorityClass = 'bg-danger';
                                else if (issue.priority === 'Medium') priorityClass = 'bg-warning';
                                else if (issue.priority === 'Low') priorityClass = 'bg-success';
                                %>
                                <span class="badge <%= priorityClass %> fs-6"><%= issue.priority %></span>
                            </div>
                        </div>

                        <!-- Description -->
                        <% if (issue.user_comment) { %>
                            <div class="mb-3">
                                <h6 class="text-muted">Description</h6>
                                <p class="mb-0"><%= issue.user_comment %></p>
                            </div>
                        <% } %>

                        <!-- Photo -->
                        <% if (issue.user_picture_path) { %>
                            <div class="mb-3">
                                <h6 class="text-muted">Photo</h6>
                                <div class="photo-preview-container">
                                    <img src="<%= issue.user_picture_path %>"
                                         alt="Issue Photo"
                                         class="issue-photo-thumbnail img-thumbnail"
                                         style="max-width: 200px; max-height: 150px; cursor: pointer;"
                                         data-bs-toggle="modal"
                                         data-bs-target="#photoModal">
                                    <div class="mt-1">
                                        <small class="text-muted">
                                            <i class="bi bi-zoom-in"></i> Click to enlarge
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <% } %>

                        <!-- Status and Dates -->
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-muted">Status</h6>
                                <% 
                                let statusClass = 'bg-warning'; // Default yellow for everything else
                                if (issue.status === 'Open') statusClass = 'bg-danger';
                                else if (issue.status === 'Resolved') statusClass = 'bg-success';
                                %>
                                <span class="badge <%= statusClass %> fs-6"><%= issue.status %></span>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-muted">Created</h6>
                                <p class="mb-0"><%= issue.formatted_created_date %></p>
                                <small class="text-muted"><%= issue.formatted_created_time %></small>
                            </div>
                            <div class="col-md-4">
                                <% if (issue.resolved_at) { %>
                                    <h6 class="text-muted">Resolved</h6>
                                    <p class="mb-0"><%= issue.formatted_resolved_date %></p>
                                    <small class="text-muted"><%= issue.formatted_resolved_time %></small>
                                <% } else { %>
                                    <h6 class="text-muted">Resolved</h6>
                                    <p class="mb-0 text-muted">Not yet resolved</p>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comments -->
                <% if (issue.tech_comment) { %>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-chat-left-text"></i>
                                Comment made by
                                <% if (issue.comment_tech_name) { %>
                                    <%= issue.comment_tech_name %> (Technician)
                                <% } else if (issue.comment_operator_name) { %>
                                    <%= issue.comment_operator_name %> (Operator)
                                <% } else { %>
                                    Unknown
                                <% } %>
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0"><%= issue.tech_comment %></p>
                        </div>
                    </div>
                <% } %>

                <!-- Recently Not Duplicated Issues -->
                <% if (notDuplicatedIssues && notDuplicatedIssues.length > 0) { %>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle text-warning"></i>
                                Recently Not Duplicated Issues
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                Similar issues on this machine that were marked as "Not Duplicated".
                                This may indicate a recurring problem.
                            </p>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Issue Type</th>
                                            <th>Assigned To</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% notDuplicatedIssues.forEach(notDupIssue => { %>
                                            <tr>
                                                <td>
                                                    <a href="/<%= operator.username %>/admin/issues/<%= notDupIssue.id %>"
                                                       class="text-decoration-none">
                                                        <strong>#<%= notDupIssue.id %></strong>
                                                    </a>
                                                </td>
                                                <td><%= notDupIssue.issue_type %></td>
                                                <td>
                                                    <% if (notDupIssue.assigned_tech_id || notDupIssue.assigned_at) { %>
                                                        <% if (notDupIssue.assigned_tech_id && notDupIssue.tech_name) { %>
                                                            <span class="text-info">
                                                                <i class="bi bi-person"></i> <%= notDupIssue.tech_name %>
                                                            </span>
                                                        <% } else { %>
                                                            <span class="text-primary">
                                                                <i class="bi bi-buildings"></i> <%= operator.name %>
                                                            </span>
                                                        <% } %>
                                                    <% } else { %>
                                                        <span class="text-muted">Not assigned</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <span class="text-muted"><%= notDupIssue.formatted_created_date %></span>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Machine Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Machine Information</h6>
                    </div>
                    <div class="card-body">
                        <h6><%= issue.machine_name %></h6>
                        <p class="text-muted mb-2">
                            <span class="badge bg-secondary"><%= issue.manufacturer %></span>
                            <span class="badge bg-secondary ms-1"><%= issue.machine_type %></span>
                        </p>
                        <% if (issue.location_notes) { %>
                            <p class="mb-2">
                                <i class="bi bi-geo-alt text-muted"></i> <%= issue.location_notes %>
                            </p>
                        <% } %>
                        <p class="mb-0">
                            <i class="bi bi-circle-fill text-<%= issue.machine_status === 'active' ? 'success' : 'secondary' %>"></i>
                            <%= issue.machine_status === 'archived' ? 'Decommissioned' : issue.machine_status %>
                        </p>
                    </div>
                </div>

                <!-- Assignment Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Assignment</h6>
                    </div>
                    <div class="card-body">
                        <% if (issue.assigned_tech_id || issue.assigned_at) { %>
                            <% if (issue.status === 'Assigned') { %>
                                <h6 class="text-success">
                                    <i class="bi bi-person-check"></i> Assigned
                                </h6>
                            <% } else { %>
                                <h6 class="text-info">
                                    <i class="bi bi-person-check"></i> Was Assigned To
                                </h6>
                            <% } %>
                            <% if (issue.assigned_tech_id && issue.tech_name) { %>
                                <!-- Assigned to a technician -->
                                <p class="mb-1"><strong><%= issue.tech_name %></strong> (Technician)</p>
                                <% if (issue.tech_email) { %>
                                    <p class="mb-2">
                                        <a href="mailto:<%= issue.tech_email %>" class="text-decoration-none">
                                            <i class="bi bi-envelope"></i> <%= issue.tech_email %>
                                        </a>
                                    </p>
                                <% } %>
                            <% } else { %>
                                <!-- Assigned to operator -->
                                <p class="mb-1"><strong><%= operator.name %></strong> (Operator)</p>
                                <% if (operator.email) { %>
                                    <p class="mb-2">
                                        <a href="mailto:<%= operator.email %>" class="text-decoration-none">
                                            <i class="bi bi-envelope"></i> <%= operator.email %>
                                        </a>
                                    </p>
                                <% } %>
                            <% } %>
                            <% if (issue.assigned_at) { %>
                                <small class="text-muted">
                                    <% if (issue.status === 'Assigned') { %>
                                        Assigned on <%= issue.formatted_assigned_date %>
                                    <% } else { %>
                                        Was assigned on <%= issue.formatted_assigned_date %>
                                    <% } %>
                                </small>
                            <% } %>
                        <% } else { %>
                            <p class="text-muted mb-0">
                                <i class="bi bi-person-x"></i> Not assigned
                            </p>
                        <% } %>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <!-- Assignment Section -->
                            <% if (issue.status !== 'Resolved' && issue.status !== 'Not Duplicated' && issue.status !== 'Assigned') { %>
                                <% if (availableTechs.length === 0) { %>
                                    <!-- No techs available - assign directly to operator -->
                                    <form method="POST" action="/<%= operator.username %>/admin/issues/<%= issue.id %>/assign" class="d-inline">
                                        <input type="hidden" name="assignee_type" value="operator">
                                        <button type="submit" class="btn btn-primary btn-sm w-100" onclick="return confirm('Assign this issue to yourself?')">
                                            <i class="bi bi-person-check"></i> Assign to Me (Operator)
                                        </button>
                                    </form>
                                <% } else { %>
                                    <!-- Techs available - show dropdown -->
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0"><i class="bi bi-person-plus"></i> Assign Issue</h6>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" action="/<%= operator.username %>/admin/issues/<%= issue.id %>/assign">
                                                <div class="mb-3">
                                                    <label for="assignee" class="form-label">Assign to:</label>
                                                    <select class="form-select" id="assignee" name="tech_id">
                                                        <option value="">Select assignee...</option>
                                                        <option value="operator">Myself (Operator)</option>
                                                        <% availableTechs.forEach(tech => { %>
                                                            <option value="<%= tech.id %>"><%= tech.name %></option>
                                                        <% }); %>
                                                    </select>
                                                    <input type="hidden" id="assignee_type" name="assignee_type" value="">
                                                </div>
                                                <button type="submit" class="btn btn-primary btn-sm w-100" id="assignButton" disabled>
                                                    <i class="bi bi-person-check"></i> Assign Issue
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                <% } %>
                            <% } %>

                            <!-- Unassign Action -->
                            <% if (issue.status === 'Assigned') { %>
                                <form method="POST" action="/<%= operator.username %>/admin/issues/<%= issue.id %>/unassign" class="d-inline">
                                    <button type="submit" class="btn btn-outline-secondary btn-sm w-100">
                                        <i class="bi bi-person-dash"></i> Unassign Issue
                                    </button>
                                </form>
                            <% } %>

                            <!-- Resolution Actions -->
                            <% if (issue.status !== 'Resolved') { %>
                                <button type="button" class="btn btn-success btn-sm w-100" data-bs-toggle="modal" data-bs-target="#resolveModal">
                                    <i class="bi bi-check-circle"></i> Mark as Resolved
                                </button>
                            <% } %>
                            <% if (issue.status !== 'Not Duplicated' && issue.status !== 'Resolved') { %>
                                <button type="button" class="btn btn-warning btn-sm w-100" data-bs-toggle="modal" data-bs-target="#notDuplicatedModal">
                                    <i class="bi bi-x-circle"></i> Issue not duplicated
                                </button>
                            <% } %>
                            <a href="/<%= operator.username %>/admin/machines/<%= issue.machine_id %>" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-controller"></i> View Machine
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Resolve Issue Modal -->
    <div class="modal fade" id="resolveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle text-success"></i> Mark Issue as Resolved
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="/<%= operator.username %>/admin/issues/<%= issue.id %>/resolve">
                    <div class="modal-body">
                        <p>Are you sure you want to mark this issue as resolved?</p>
                        <div class="mb-3">
                            <label for="tech_comment_resolve" class="form-label">Resolution Comments (Optional)</label>
                            <textarea class="form-control" id="tech_comment_resolve" name="tech_comment" rows="3"
                                      placeholder="Add any comments about the resolution..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> Mark as Resolved
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Not Duplicated Modal -->
    <div class="modal fade" id="notDuplicatedModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-x-circle text-warning"></i> Mark Issue as Not Duplicated
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="/<%= operator.username %>/admin/issues/<%= issue.id %>/not-duplicated">
                    <div class="modal-body">
                        <p>Are you sure this issue is not a duplicate? This will close the issue.</p>
                        <div class="mb-3">
                            <label for="tech_comment_not_dup" class="form-label">Comments (Optional)</label>
                            <textarea class="form-control" id="tech_comment_not_dup" name="tech_comment" rows="3"
                                      placeholder="Add any comments about why this is not a duplicate..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-x-circle"></i> Mark as Not Duplicated
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Photo Modal -->
    <% if (issue.user_picture_path) { %>
        <div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="photoModalLabel">
                            <i class="bi bi-image"></i> Issue Photo
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="<%= issue.user_picture_path %>"
                             alt="Issue Photo"
                             class="img-fluid"
                             style="max-width: 100%; height: auto;">
                    </div>
                    <div class="modal-footer">
                        <a href="<%= issue.user_picture_path %>"
                           download="issue-<%= issue.id %>-photo.jpg"
                           class="btn btn-primary">
                            <i class="bi bi-download"></i> Download
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <% } %>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
    <!-- Issue Details JS -->
    <script src="/static/js/issue-details.js"></script>
</body>
</html>
