// Camera functionality for issue reporting
document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss error alerts after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert-danger');
        alerts.forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const issueType = document.getElementById('issue_type').value;
        const priority = document.getElementById('priority').value;
        
        if (!issueType || !priority) {
            e.preventDefault();
            alert('Please fill in all required fields (Issue Type and Priority)');
            return false;
        }
    });

    // Camera functionality
    let stream = null;
    let photoTaken = false;

    const startCameraBtn = document.getElementById('start-camera');
    const stopCameraBtn = document.getElementById('stop-camera');
    const captureBtn = document.getElementById('capture-photo');
    const retakeBtn = document.getElementById('retake-photo');
    const removeBtn = document.getElementById('remove-photo');
    const photoUpload = document.getElementById('photo-upload');
    
    const cameraSection = document.getElementById('camera-section');
    const cameraPreview = document.getElementById('camera-preview');
    const photoPreview = document.getElementById('photo-preview');
    const fileUploadSection = document.getElementById('file-upload-section');
    
    const video = document.getElementById('camera-video');
    const canvas = document.getElementById('photo-canvas');
    const photoFileInput = document.getElementById('photo-file');

    // Start camera
    startCameraBtn.addEventListener('click', async () => {
        try {
            stream = await navigator.mediaDevices.getUserMedia({ 
                video: { 
                    facingMode: 'environment', // Use back camera on mobile
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                } 
            });
            video.srcObject = stream;
            
            cameraSection.classList.add('d-none');
            cameraPreview.classList.remove('d-none');
            fileUploadSection.classList.add('d-none');
        } catch (error) {
            console.error('Error accessing camera:', error);
            alert('Unable to access camera. Please use the upload option instead.');
        }
    });

    // Stop camera
    stopCameraBtn.addEventListener('click', () => {
        stopCamera();
        cameraSection.classList.remove('d-none');
        cameraPreview.classList.add('d-none');
        fileUploadSection.classList.remove('d-none');
    });

    // Capture photo
    captureBtn.addEventListener('click', () => {
        const context = canvas.getContext('2d');

        // Compress image by limiting resolution
        const maxWidth = 1920;
        const maxHeight = 1080;
        let { width, height } = video;

        // Calculate new dimensions while maintaining aspect ratio
        if (width > height) {
            if (width > maxWidth) {
                height = (height * maxWidth) / width;
                width = maxWidth;
            }
        } else {
            if (height > maxHeight) {
                width = (width * maxHeight) / height;
                height = maxHeight;
            }
        }

        canvas.width = width;
        canvas.height = height;
        context.drawImage(video, 0, 0, width, height);

        // Set canvas CSS to maintain aspect ratio
        setCanvasAspectRatio(canvas);

        // Convert canvas to blob with compression
        canvas.toBlob((blob) => {
            const file = new File([blob], 'issue-photo.jpg', { type: 'image/jpeg' });
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            photoFileInput.files = dataTransfer.files;
            photoTaken = true;
        }, 'image/jpeg', 0.7); // Reduced quality for smaller file size

        stopCamera();
        cameraPreview.classList.add('d-none');
        photoPreview.classList.remove('d-none');
    });

    // Retake photo
    retakeBtn.addEventListener('click', () => {
        photoPreview.classList.add('d-none');
        cameraSection.classList.remove('d-none');
        fileUploadSection.classList.remove('d-none');
        photoFileInput.value = '';
        photoTaken = false;
    });

    // Remove photo
    removeBtn.addEventListener('click', () => {
        photoPreview.classList.add('d-none');
        cameraSection.classList.remove('d-none');
        fileUploadSection.classList.remove('d-none');
        photoFileInput.value = '';
        photoTaken = false;
    });

    // File upload
    photoUpload.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            // Check file size before processing
            if (file.size > 25 * 1024 * 1024) { // 25MB
                alert('File is too large. Please choose a smaller image (max 25MB).');
                e.target.value = '';
                return;
            }

            // Show preview and compress
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    const context = canvas.getContext('2d');

                    // Compress image by limiting resolution
                    const maxWidth = 1920;
                    const maxHeight = 1080;
                    let { width, height } = img;

                    // Calculate new dimensions while maintaining aspect ratio
                    if (width > height) {
                        if (width > maxWidth) {
                            height = (height * maxWidth) / width;
                            width = maxWidth;
                        }
                    } else {
                        if (height > maxHeight) {
                            width = (width * maxHeight) / height;
                            height = maxHeight;
                        }
                    }

                    canvas.width = width;
                    canvas.height = height;
                    context.drawImage(img, 0, 0, width, height);

                    // Set canvas CSS to maintain aspect ratio
                    setCanvasAspectRatio(canvas);

                    // Convert to compressed blob and create new file
                    canvas.toBlob((blob) => {
                        const compressedFile = new File([blob], file.name, { type: 'image/jpeg' });
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(compressedFile);
                        photoFileInput.files = dataTransfer.files;
                        photoTaken = true;
                    }, 'image/jpeg', 0.7); // Compressed quality

                    cameraSection.classList.add('d-none');
                    fileUploadSection.classList.add('d-none');
                    photoPreview.classList.remove('d-none');
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Helper function to stop camera
    function stopCamera() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
    }

    // Helper function to set canvas aspect ratio
    function setCanvasAspectRatio(canvas) {
        const aspectRatio = canvas.width / canvas.height;
        const maxWidth = 400; // Maximum display width
        const maxHeight = 300; // Maximum display height

        let displayWidth, displayHeight;

        if (aspectRatio > 1) {
            // Landscape: width is larger
            displayWidth = Math.min(maxWidth, canvas.width);
            displayHeight = displayWidth / aspectRatio;
        } else {
            // Portrait: height is larger
            displayHeight = Math.min(maxHeight, canvas.height);
            displayWidth = displayHeight * aspectRatio;
        }

        // Set CSS dimensions to maintain aspect ratio
        canvas.style.width = displayWidth + 'px';
        canvas.style.height = displayHeight + 'px';
        canvas.style.maxWidth = '100%';
        canvas.style.height = 'auto';
        canvas.style.objectFit = 'contain';
    }

    // Clean up camera on page unload
    window.addEventListener('beforeunload', stopCamera);
});
