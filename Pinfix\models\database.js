const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

class Database {
  constructor() {
    this.dbPath = process.env.DATABASE_PATH || './data/pinfix.db';
    this.db = null;
  }

  async connect() {
    return new Promise((resolve, reject) => {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // Check if database file exists or is empty
      const dbExists = fs.existsSync(this.dbPath);
      const dbIsEmpty = !dbExists || fs.statSync(this.dbPath).size === 0;

      this.db = new sqlite3.Database(this.dbPath, async (err) => {
        if (err) {
          console.error('Error opening database:', err);
          reject(err);
        } else {
          console.log('Connected to SQLite database');
          // Enable foreign keys
          this.db.run('PRAGMA foreign_keys = ON');

          // Auto-initialize if database is new or empty
          if (dbIsEmpty) {
            console.log('🔄 Database is empty or missing - auto-initializing...');
            try {
              await this.autoInitialize();
              console.log('✅ Database auto-initialization completed successfully!');
            } catch (initError) {
              console.error('❌ Database auto-initialization failed:', initError);
              reject(initError);
              return;
            }
          }

          resolve();
        }
      });
    });
  }

  async close() {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            reject(err);
          } else {
            console.log('Database connection closed');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  async run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  async get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  async all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async autoInitialize() {
    const bcrypt = require('bcryptjs');
    require('dotenv').config();

    console.log('🔧 Creating database tables...');
    await this.initializeTables();

    console.log('👤 Creating super admin user...');
    const superAdminUsername = process.env.SUPER_ADMIN_USERNAME || 'superadmin';
    const superAdminPassword = process.env.SUPER_ADMIN_PASSWORD || 'admin123';

    // Check if super admin already exists
    const existingSuperAdmin = await this.get(
      'SELECT id FROM super_admin WHERE username = ?',
      [superAdminUsername]
    );

    if (!existingSuperAdmin) {
      const hashedPassword = await bcrypt.hash(superAdminPassword, 10);
      await this.run(
        'INSERT INTO super_admin (username, password_hash) VALUES (?, ?)',
        [superAdminUsername, hashedPassword]
      );
      console.log(`✅ Super admin created with username: ${superAdminUsername}`);
      console.log(`🔑 Super admin password: ${superAdminPassword}`);
      console.log('⚠️  IMPORTANT: Change the super admin password in production!');
    }

    console.log('🎮 Adding sample pinball machines...');
    await this.addSampleMachines();
  }

  async initializeTables() {
    const tables = [
      // Operators table
      `CREATE TABLE IF NOT EXISTS operators (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        logo_path TEXT,
        address TEXT,
        phone TEXT,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
        email_notifications_enabled INTEGER DEFAULT 0,
        operator_notifications_enabled INTEGER DEFAULT 0,
        tech_notifications_enabled INTEGER DEFAULT 0,
        machine_limit INTEGER DEFAULT 10,
        timezone_offset INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Global pinball machines database
      `CREATE TABLE IF NOT EXISTS global_pinball_machines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        manufacturer TEXT NOT NULL CHECK (manufacturer IN (
          'Stern', 'Jersey Jack Pinball', 'Spooky Pinball', 
          'Chicago Gaming Company', 'Multimorphic', 'Pinball Brothers',
          'Bally', 'Williams', 'Gottlieb', 'Other'
        )),
        date_of_manufacture INTEGER,
        machine_type TEXT NOT NULL CHECK (machine_type IN ('EM', 'SS Alpha', 'SS DMD', 'SS LCD')),
        picture_path TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(name, manufacturer)
      )`,

      // Pinball machines
      `CREATE TABLE IF NOT EXISTS pinball_machines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operator_id INTEGER NOT NULL,
        global_machine_id INTEGER NOT NULL,
        qr_code_path TEXT,
        date_added DATETIME DEFAULT CURRENT_TIMESTAMP,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived')),
        location_notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (operator_id) REFERENCES operators (id) ON DELETE CASCADE,
        FOREIGN KEY (global_machine_id) REFERENCES global_pinball_machines (id)
      )`,

      // Techs table
      `CREATE TABLE IF NOT EXISTS techs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operator_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        username TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (operator_id) REFERENCES operators (id) ON DELETE CASCADE,
        UNIQUE(operator_id, username)
      )`,

      // Issues table
      `CREATE TABLE IF NOT EXISTS issues (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operator_id INTEGER NOT NULL,
        machine_id INTEGER NOT NULL,
        global_machine_id INTEGER NOT NULL,
        issue_type TEXT NOT NULL CHECK (issue_type IN (
          'Flipper not working', 'Flipper weak or Sticking', 'Ball not ejecting',
          'Ball Stuck on Playfield', 'Multiple balls ejecting unexpectedly',
          'Pop bumper not firing', 'Slingshot not firing', 'Switch not registering hits',
          'Drop target not resetting', 'General illumination out', 'Feature lamp not working',
          'Vertical Up-Kicker not working or weak', 'Sound distorted'
        )),
        priority TEXT DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High')),
        user_comment TEXT,
        user_picture_path TEXT,
        status TEXT DEFAULT 'Open' CHECK (status IN ('Open', 'Assigned', 'Resolved', 'Not Duplicated')),
        assigned_tech_id INTEGER,
        tech_comment TEXT,
        resolved_by_tech_id INTEGER,
        resolved_by_operator_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        assigned_at DATETIME,
        resolved_at DATETIME,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (operator_id) REFERENCES operators (id) ON DELETE CASCADE,
        FOREIGN KEY (machine_id) REFERENCES pinball_machines (id) ON DELETE CASCADE,
        FOREIGN KEY (global_machine_id) REFERENCES global_pinball_machines (id),
        FOREIGN KEY (assigned_tech_id) REFERENCES techs (id),
        FOREIGN KEY (resolved_by_tech_id) REFERENCES techs (id),
        FOREIGN KEY (resolved_by_operator_id) REFERENCES operators (id)
      )`,

      // Super admin table
      `CREATE TABLE IF NOT EXISTS super_admin (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Activity log table for super admin dashboard
      `CREATE TABLE IF NOT EXISTS activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        activity_type TEXT NOT NULL CHECK (activity_type IN (
          'issue_created', 'issue_assigned', 'issue_resolved', 'issue_not_duplicated',
          'operator_created', 'operator_status_changed', 'machine_added', 'tech_added'
        )),
        operator_id INTEGER,
        machine_id INTEGER,
        issue_id INTEGER,
        tech_id INTEGER,
        actor_type TEXT CHECK (actor_type IN ('operator', 'tech', 'system', 'super_admin')),
        actor_name TEXT,
        description TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (operator_id) REFERENCES operators (id) ON DELETE CASCADE,
        FOREIGN KEY (machine_id) REFERENCES pinball_machines (id) ON DELETE CASCADE,
        FOREIGN KEY (issue_id) REFERENCES issues (id) ON DELETE CASCADE,
        FOREIGN KEY (tech_id) REFERENCES techs (id) ON DELETE CASCADE
      )`
    ];

    for (const table of tables) {
      await this.run(table);
    }
  }

  async addSampleMachines() {
    const csvPath = path.join(__dirname, '..', 'data', 'pinball.csv');

    // Check if CSV file exists
    if (!fs.existsSync(csvPath)) {
      console.log('  ⚠️  pinball.csv not found, skipping machine data import');
      return;
    }

    try {
      // Read and parse CSV file
      const csvContent = fs.readFileSync(csvPath, 'utf8');
      const lines = csvContent.split('\n').filter(line => line.trim());

      if (lines.length === 0) {
        console.log('  ⚠️  pinball.csv is empty');
        return;
      }

      // Parse header
      const header = lines[0].split(',').map(col => col.trim());
      console.log(`  📄 Found ${lines.length - 1} machines in pinball.csv`);

      let addedCount = 0;
      let skippedCount = 0;

      // Process each machine (skip header)
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(val => val.trim());

        if (values.length !== header.length) {
          console.log(`  ⚠️  Skipping malformed line ${i + 1}: ${lines[i]}`);
          continue;
        }

        const machine = {
          manufacturer: values[0],
          name: values[1],
          date_of_manufacture: parseInt(values[2]) || null,
          machine_type: values[3]
        };

        // Skip if essential data is missing
        if (!machine.manufacturer || !machine.name) {
          console.log(`  ⚠️  Skipping incomplete machine data at line ${i + 1}`);
          continue;
        }

        // Check if machine already exists
        const existing = await this.get(
          'SELECT id FROM global_pinball_machines WHERE name = ? AND manufacturer = ?',
          [machine.name, machine.manufacturer]
        );

        if (!existing) {
          await this.run(
            'INSERT INTO global_pinball_machines (name, manufacturer, date_of_manufacture, machine_type) VALUES (?, ?, ?, ?)',
            [machine.name, machine.manufacturer, machine.date_of_manufacture, machine.machine_type]
          );
          addedCount++;
        } else {
          skippedCount++;
        }
      }

      console.log(`  ✅ Added ${addedCount} new machines, skipped ${skippedCount} existing machines`);

    } catch (error) {
      console.error('  ❌ Error reading pinball.csv:', error.message);
    }
  }
}

module.exports = new Database();
