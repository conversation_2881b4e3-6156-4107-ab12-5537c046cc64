// Issue details page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Assignment dropdown functionality
    const assigneeSelect = document.getElementById('assignee');
    const assigneeTypeInput = document.getElementById('assignee_type');
    const assignButton = document.getElementById('assignButton');

    if (assigneeSelect && assigneeTypeInput && assignButton) {
        // Add change event listener to the select element
        assigneeSelect.addEventListener('change', function() {
            updateAssigneeType(this);
        });

        // Update assignee type and enable/disable button
        function updateAssigneeType(selectElement) {
            const value = selectElement.value;

            if (value === '') {
                assigneeTypeInput.value = '';
                assignButton.disabled = true;
            } else if (value === 'operator') {
                assigneeTypeInput.value = 'operator';
                assignButton.disabled = false;
            } else {
                assigneeTypeInput.value = 'tech';
                assignButton.disabled = false;
            }
        }
    }
});
