"""
Data Extraction and Conversion Module for Own Ship Extractor
Handles extraction and conversion of data from TimeZero format to readable values
"""

import sqlite3
import datetime
import pandas as pd
import math
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass


@dataclass
class OwnShipRecord:
    """Data class for a single own ship record"""
    datetime: datetime.datetime
    latitude: Optional[float]
    longitude: Optional[float]
    course_over_ground: Optional[float]
    heading: Optional[float]
    speed_through_water: Optional[float]
    speed_over_ground: Optional[float]
    depth: Optional[float]
    sea_surface_temperature: Optional[float]
    true_wind_direction: Optional[float]
    true_wind_speed: Optional[float]
    apparent_wind_angle: Optional[float]
    apparent_wind_speed: Optional[float]
    air_temperature: Optional[float]
    atmospheric_pressure: Optional[float]
    humidity: Optional[float]
    roll: Optional[float]
    pitch: Optional[float]
    heave: Optional[float]


class DataExtractor:
    """Extracts and converts data from OwnShipRecorder database"""

    # TimeZero epoch (2000-01-01 00:00:00 UTC)
    TZ_EPOCH = datetime.datetime(2000, 1, 1, tzinfo=datetime.timezone.utc)

    # WGS84 constants for Mercator projection (from MercatorTools.cs)
    WGS84_EQUATORIAL_RADIUS_IN_METER = 6378137.0
    WGS84_FLATTENING_RATIO = 298.257223563
    WGS84_FLATTENING = 1.0 / WGS84_FLATTENING_RATIO
    WGS84_SQUARE_ECCENTRICITY = (2.0 * WGS84_FLATTENING) - (WGS84_FLATTENING * WGS84_FLATTENING)
    WGS84_ECCENTRICITY = math.sqrt(WGS84_SQUARE_ECCENTRICITY)
    ONE_CENTIMETER_RAD = 1.570670673141045335604473333773e-9

    # Scaling factors for different data types
    ANGLE_SCALE_10 = 10.0         # Some angles in 0.1 degree precision (COG, Heading, Wind angles)
    ANGLE_SCALE_100 = 100.0       # Other angles in 0.01 degree precision
    SPEED_SCALE = 100.0           # Speeds in 0.01 knot precision
    DEPTH_SCALE = 100.0           # Depth in cm, convert to meters
    TEMPERATURE_SCALE = 10.0      # Temperature in 0.1 degree precision
    PRESSURE_SCALE = 1.0          # Pressure - no scaling needed
    PERCENTAGE_SCALE = 100.0      # Percentages in 0.01% precision
    DISTANCE_SCALE = 100.0        # Distances in cm, convert to meters
    
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def _convert_timestamp(self, tz_timestamp: int) -> datetime.datetime:
        """Convert TimeZero timestamp to datetime using the exact SQL formula"""
        # SQL: strftime('%Y-%m-%d %H:%M:%S',strftime('%s','2000-01-01 00:00:00')-strftime('%s','1970-01-01 00:00:00')+Date,'unixepoch','localtime')
        # This is: unix_timestamp = 946684800 + tz_timestamp, then convert to local time

        # The offset from 1970-01-01 to 2000-01-01 in seconds
        tz2000_offset = 946684800

        # Convert TimeZero timestamp to Unix timestamp
        unix_timestamp = tz2000_offset + tz_timestamp

        # Convert from Unix timestamp to local time (this matches the SQL exactly)
        local_dt = datetime.datetime.fromtimestamp(unix_timestamp)

        # Return as timezone-aware datetime (local timezone)
        return local_dt.replace(tzinfo=datetime.timezone.utc)
    
    def _scale_value(self, value: int, scale: float, null_value: int = 0) -> Optional[float]:
        """Convert scaled integer to float, handling null values"""
        if value == null_value or value is None:
            return None
        return value / scale
    
    def _mercator_x_to_longitude(self, x_centimeter: int) -> Optional[float]:
        """Convert Mercator X coordinate in centimeters to WGS84 longitude in degrees"""
        if x_centimeter == 0:
            return None

        x_meter = x_centimeter / 100.0  # Convert from centimeter to meter
        longitude_radian = x_meter / self.WGS84_EQUATORIAL_RADIUS_IN_METER
        longitude_degree = math.degrees(longitude_radian)
        return longitude_degree

    def _mercator_y_to_latitude(self, y_centimeter: int) -> Optional[float]:
        """Convert Mercator Y coordinate in centimeters to WGS84 latitude in degrees"""
        if y_centimeter == 0:
            return None

        y_meter = y_centimeter / 100.0  # Convert from centimeter to meter
        phi = (math.pi / 2.0) - (2.0 * math.atan(-y_meter / self.WGS84_EQUATORIAL_RADIUS_IN_METER))

        # Iterative calculation for accurate conversion
        i = 0
        d_phi = 0
        while i < 15:
            con = self.WGS84_ECCENTRICITY * math.sin(phi)
            old_phi = phi
            phi = (math.pi / 2.0) - (2.0 * math.atan(
                math.exp(-y_meter / self.WGS84_EQUATORIAL_RADIUS_IN_METER) *
                math.pow((1.0 - con) / (1.0 + con), (self.WGS84_ECCENTRICITY / 2.0))
            ))
            d_phi = abs(old_phi - phi)
            i += 1
            if d_phi <= self.ONE_CENTIMETER_RAD:
                break

        latitude_degree = math.degrees(phi)
        return latitude_degree
    
    def get_date_range(self) -> Tuple[Optional[datetime.datetime], Optional[datetime.datetime]]:
        """Get the date range of available data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT MIN(Date), MAX(Date) FROM Data")
            min_date, max_date = cursor.fetchone()
            
            conn.close()
            
            if min_date and max_date:
                return (self._convert_timestamp(min_date), 
                       self._convert_timestamp(max_date))
            return None, None
            
        except Exception as e:
            print(f"Error getting date range: {e}")
            return None, None
    
    def extract_data(self, start_time: Optional[datetime.datetime] = None,
                    end_time: Optional[datetime.datetime] = None,
                    progress_callback=None) -> List[OwnShipRecord]:
        """
        Extract data from the database within the specified time range
        If no time range specified, extracts all data
        progress_callback: Optional function to call with progress updates (0-100)
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if progress_callback:
                progress_callback("Connecting to database...")

            # Build query with optional time filtering
            query = """
                SELECT Date, X, Y, CourseOverGround, Heading, SpeedThroughWater,
                       SpeedOverGround, Depth, SeaSurfaceTemperature, TrueWindDirection,
                       TrueWindSpeed, ApparentWindAngle, ApparentWindSpeed,
                       AirTemperature, AtmosphericPressure, OutsideHumidity,
                       Roll, Pitch, Heaving
                FROM Data
            """

            params = []
            if start_time or end_time:
                conditions = []
                if start_time:
                    # Convert datetime to TimeZero timestamp (seconds since 2000-01-01)
                    tz2000_epoch = datetime.datetime(2000, 1, 1, tzinfo=datetime.timezone.utc)
                    start_tz_timestamp = int((start_time - tz2000_epoch).total_seconds())
                    conditions.append("Date >= ?")
                    params.append(start_tz_timestamp)
                if end_time:
                    # Convert datetime to TimeZero timestamp (seconds since 2000-01-01)
                    tz2000_epoch = datetime.datetime(2000, 1, 1, tzinfo=datetime.timezone.utc)
                    end_tz_timestamp = int((end_time - tz2000_epoch).total_seconds())
                    conditions.append("Date <= ?")
                    params.append(end_tz_timestamp)

                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY Date"

            if progress_callback:
                progress_callback("Executing database query...")

            cursor.execute(query, params)
            rows = cursor.fetchall()

            conn.close()

            if progress_callback:
                progress_callback("Processing data records...")
            
            # Convert rows to OwnShipRecord objects
            records = []
            total_rows = len(rows)

            for i, row in enumerate(rows):
                # Update progress every 1000 records or at the end
                if progress_callback and (i % 1000 == 0 or i == total_rows - 1):
                    progress_percent = int((i / total_rows) * 100) if total_rows > 0 else 100
                    progress_callback(f"Converting records... {progress_percent}% ({i+1}/{total_rows})")
                (date_ms, x, y, cog, heading, stw, sog, depth, sst, twd,
                 tws, awa, aws, air_temp, pressure, humidity, roll, pitch, heave) = row
                
                record = OwnShipRecord(
                    datetime=self._convert_timestamp(date_ms),
                    latitude=self._mercator_y_to_latitude(y),
                    longitude=self._mercator_x_to_longitude(x),
                    course_over_ground=self._scale_value(cog, self.ANGLE_SCALE_10),
                    heading=self._scale_value(heading, self.ANGLE_SCALE_10),
                    speed_through_water=self._scale_value(stw, self.SPEED_SCALE),
                    speed_over_ground=self._scale_value(sog, self.SPEED_SCALE),
                    depth=self._scale_value(depth, self.DEPTH_SCALE),
                    sea_surface_temperature=self._scale_value(sst, self.TEMPERATURE_SCALE),
                    true_wind_direction=self._scale_value(twd, self.ANGLE_SCALE_10),
                    true_wind_speed=self._scale_value(tws, self.SPEED_SCALE),
                    apparent_wind_angle=self._scale_value(awa, self.ANGLE_SCALE_10),
                    apparent_wind_speed=self._scale_value(aws, self.SPEED_SCALE),
                    air_temperature=self._scale_value(air_temp, self.TEMPERATURE_SCALE),
                    atmospheric_pressure=self._scale_value(pressure, self.PRESSURE_SCALE),
                    humidity=self._scale_value(humidity, self.PERCENTAGE_SCALE),
                    roll=self._scale_value(roll, self.ANGLE_SCALE_10),
                    pitch=self._scale_value(pitch, self.ANGLE_SCALE_10),
                    heave=self._scale_value(heave, self.DISTANCE_SCALE)
                )
                records.append(record)

            if progress_callback:
                progress_callback("Data extraction complete!")

            return records
            
        except Exception as e:
            print(f"Error extracting data: {e}")
            return []
    
    def to_dataframe(self, records: List[OwnShipRecord], use_local_time: bool = False) -> pd.DataFrame:
        """Convert records to pandas DataFrame"""
        data = []
        
        for record in records:
            # Convert datetime to local time if requested
            dt = record.datetime
            if use_local_time:
                dt = dt.replace(tzinfo=datetime.timezone.utc).astimezone()
            
            data.append({
                'Date & Time': dt.strftime('%Y-%m-%d %H:%M:%S'),
                'Latitude': record.latitude,
                'Longitude': record.longitude,
                'Course Over Ground': record.course_over_ground,
                'Heading': record.heading,
                'Speed Through Water': record.speed_through_water,
                'Speed Over Ground': record.speed_over_ground,
                'Depth': record.depth,
                'Sea Surface Temperature': record.sea_surface_temperature,
                'True Wind Direction': record.true_wind_direction,
                'True Wind Speed': record.true_wind_speed,
                'Apparent Wind Angle': record.apparent_wind_angle,
                'Apparent Wind Speed': record.apparent_wind_speed,
                'Air Temperature': record.air_temperature,
                'Atmospheric Pressure': record.atmospheric_pressure,
                'Humidity': record.humidity,
                'Roll': record.roll,
                'Pitch': record.pitch,
                'Heave': record.heave
            })
        
        return pd.DataFrame(data)
    
    def get_data_for_timeframe(self, hours_back: int, progress_callback=None) -> List[OwnShipRecord]:
        """Get data for the last N hours from the most recent entry"""
        try:
            if progress_callback:
                progress_callback("Finding latest data timestamp...")

            # Get the latest timestamp
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT MAX(Date) FROM Data")
            max_date_ms = cursor.fetchone()[0]
            conn.close()

            if not max_date_ms:
                return []

            if progress_callback:
                progress_callback("Calculating time range...")

            # Calculate start time
            end_time = self._convert_timestamp(max_date_ms)
            start_time = end_time - datetime.timedelta(hours=hours_back)

            return self.extract_data(start_time, end_time, progress_callback)

        except Exception as e:
            print(f"Error getting data for timeframe: {e}")
            return []


if __name__ == "__main__":
    # Test the data extractor
    extractor = DataExtractor("OwnShipRecorder.tzdb")
    
    print("Getting date range...")
    start_date, end_date = extractor.get_date_range()
    print(f"Date range: {start_date} to {end_date}")
    
    print("\nExtracting last 100 records...")
    records = extractor.extract_data()[-100:]  # Last 100 records
    
    if records:
        print(f"Extracted {len(records)} records")
        print("\nFirst few records:")
        for i, record in enumerate(records[:3]):
            print(f"  {i+1}: {record.datetime} - Lat: {record.latitude}, Lon: {record.longitude}")
            print(f"      COG: {record.course_over_ground}°, SOG: {record.speed_over_ground}kn")
        
        # Test DataFrame conversion
        df = extractor.to_dataframe(records[:10])
        print(f"\nDataFrame shape: {df.shape}")
        print("Columns:", list(df.columns))
    else:
        print("No records extracted")
