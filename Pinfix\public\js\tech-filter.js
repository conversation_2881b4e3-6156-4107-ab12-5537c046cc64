// Tech dashboard filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    let activeFilter = null;

    // Add click handlers to filter cards
    document.querySelectorAll('.filter-card').forEach(card => {
        card.addEventListener('click', function() {
            const status = this.getAttribute('data-status');
            
            console.log('Card clicked:', status); // Debug log
            
            if (activeFilter === status) {
                // If clicking the same filter, clear it
                clearFilters();
            } else {
                // Apply new filter
                applyFilter(status);
            }
        });
    });

    // Clear filters button
    const clearButton = document.getElementById('clear-filters');
    if (clearButton) {
        clearButton.addEventListener('click', clearFilters);
    }

    function applyFilter(status) {
        console.log('Applying filter:', status); // Debug log
        activeFilter = status;
        
        // Update card appearances
        document.querySelectorAll('.filter-card').forEach(card => {
            if (card.getAttribute('data-status') === status) {
                card.style.boxShadow = '0 0 0 3px rgba(13, 110, 253, 0.25)';
                card.style.borderColor = '#0d6efd';
            } else {
                card.style.boxShadow = '';
                card.style.borderColor = '';
                card.style.opacity = '0.6';
            }
        });

        // Filter table rows
        let visibleCount = 0;
        document.querySelectorAll('.issue-row').forEach(row => {
            let shouldShow = false;
            
            if (status === 'all') {
                shouldShow = true;
            } else {
                shouldShow = row.getAttribute('data-status') === status;
            }
            
            if (shouldShow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        console.log('Visible rows:', visibleCount); // Debug log

        // Show filter status and clear button
        const filterStatus = document.getElementById('filter-status');
        const clearButton = document.getElementById('clear-filters');
        
        if (filterStatus) {
            filterStatus.textContent = `Showing: ${getStatusDisplayName(status)}`;
            filterStatus.style.display = 'inline';
        }
        if (clearButton) {
            clearButton.style.display = 'inline-block';
        }
    }

    function clearFilters() {
        console.log('Clearing filters'); // Debug log
        activeFilter = null;
        
        // Reset card appearances
        document.querySelectorAll('.filter-card').forEach(card => {
            card.style.boxShadow = '';
            card.style.borderColor = '';
            card.style.opacity = '';
        });

        // Show all table rows
        document.querySelectorAll('.issue-row').forEach(row => {
            row.style.display = '';
        });

        // Hide filter status and clear button
        const filterStatus = document.getElementById('filter-status');
        const clearButton = document.getElementById('clear-filters');
        
        if (filterStatus) {
            filterStatus.style.display = 'none';
        }
        if (clearButton) {
            clearButton.style.display = 'none';
        }
    }

    function getStatusDisplayName(status) {
        switch(status) {
            case 'all': return 'All Issues';
            case 'Assigned': return 'Assigned';
            case 'Resolved': return 'Resolved';
            case 'Not Duplicated': return 'Not Duplicated';
            default: return status;
        }
    }
});
