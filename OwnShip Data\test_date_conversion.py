"""
Test script to verify date conversion matches SQL formula
"""

import sqlite3
import datetime
from data_extractor import DataExtractor

def test_date_conversion():
    """Compare Python date conversion with SQL conversion"""
    
    extractor = DataExtractor('OwnShipRecorder.tzdb')
    conn = sqlite3.connect('OwnShipRecorder.tzdb')
    cursor = conn.cursor()
    
    # Get sample data with both raw and SQL-converted dates (using localtime like original)
    query = """
        SELECT Date,
               strftime('%Y-%m-%d %H:%M:%S',
                       strftime('%s','2000-01-01 00:00:00')-strftime('%s','1970-01-01 00:00:00')+Date,
                       'unixepoch','localtime') as SQLDate
        FROM Data
        LIMIT 5
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    
    print("Comparison of Python vs SQL date conversion:")
    print("Raw Timestamp | SQL Conversion      | Python Conversion   | Match")
    print("-" * 70)
    
    for row in rows:
        raw_timestamp = row[0]
        sql_date = row[1]
        # Get Python conversion (should now match SQL exactly)
        python_dt = extractor._convert_timestamp(raw_timestamp)
        python_date = python_dt.strftime('%Y-%m-%d %H:%M:%S')
        
        match = "✓" if sql_date == python_date else "✗"
        print(f"{raw_timestamp:12} | {sql_date} | {python_date} | {match}")
    
    conn.close()

if __name__ == "__main__":
    test_date_conversion()
