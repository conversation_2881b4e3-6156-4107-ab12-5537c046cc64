apiVersion: v1
kind: Secret
metadata:
  name: call-analysis-secrets
  labels:
    app: call-analysis
type: Opaque
data:
  # Base64 encoded API keys - replace with your actual encoded keys
  # To encode: echo -n "your-api-key" | base64
  
  # AssemblyAI API Key
  ASSEMBLYAI_API_KEY: ""  # Replace with: echo -n "your-assemblyai-key" | base64
  
  # OpenAI API Key
  OPENAI_API_KEY: ""  # Replace with: echo -n "your-openai-key" | base64
  
  # RingCentral credentials JSON (base64 encoded)
  RC_CREDENTIALS_JSON: ""  # Replace with: cat rc-credentials.json | base64 -w 0

---
# Example of how to create the secret using kubectl (commented out)
# kubectl create secret generic call-analysis-secrets \
#   --from-literal=ASSEMBLYAI_API_KEY="your-assemblyai-key" \
#   --from-literal=OPENAI_API_KEY="your-openai-key" \
#   --from-file=RC_CREDENTIALS_JSON=rc-credentials.json
