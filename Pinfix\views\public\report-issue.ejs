<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Main Content -->
    <main class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="h2 mb-3">
                        <i class="bi bi-exclamation-triangle text-warning"></i> Report Issue
                    </h1>
                    <div class="card bg-primary text-white mb-4">
                        <div class="card-body text-center">
                            <h5 class="card-title mb-1">
                                <i class="bi bi-controller"></i> <%= machine.name %>
                            </h5>
                            <p class="card-text mb-0">
                                <small>Operated by <%= operator.name %></small>
                            </p>
                            <% if (machine.location_notes) { %>
                                <p class="card-text mb-0">
                                    <small><i class="bi bi-geo-alt"></i> <%= machine.location_notes %></small>
                                </p>
                            <% } %>
                        </div>
                    </div>
                </div>

                <!-- Error Messages -->
                <% if (typeof error !== 'undefined') { %>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>

                <!-- Issue Form -->
                <div class="card shadow">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">What's the problem?</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/<%= operator.username %>/machine/<%= machine.id %>/report" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="issue_type" class="form-label">Issue Type *</label>
                                <select class="form-select" id="issue_type" name="issue_type" required>
                                    <option value="">Select issue type...</option>
                                    <option value="Flipper not working" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Flipper not working') ? 'selected' : '' %>>Flipper not working</option>
                                    <option value="Flipper weak or Sticking" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Flipper weak or Sticking') ? 'selected' : '' %>>Flipper weak or Sticking</option>
                                    <option value="Ball not ejecting" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Ball not ejecting') ? 'selected' : '' %>>Ball not ejecting</option>
                                    <option value="Ball Stuck on Playfield" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Ball Stuck on Playfield') ? 'selected' : '' %>>Ball Stuck on Playfield</option>
                                    <option value="Pop bumper not firing" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Pop bumper not firing') ? 'selected' : '' %>>Pop bumper not firing</option>
                                    <option value="Slingshot not firing" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Slingshot not firing') ? 'selected' : '' %>>Slingshot not firing</option>
                                    <option value="Switch not registering hits" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Switch not registering hits') ? 'selected' : '' %>>Switch not registering hits</option>
                                    <option value="Drop target not resetting" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Drop target not resetting') ? 'selected' : '' %>>Drop target not resetting</option>
                                    <option value="General illumination out" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'General illumination out') ? 'selected' : '' %>>General illumination out</option>
                                    <option value="Feature lamp not working" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Feature lamp not working') ? 'selected' : '' %>>Feature lamp not working</option>
                                    <option value="Vertical Up-Kicker not working or weak" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Vertical Up-Kicker not working or weak') ? 'selected' : '' %>>Vertical Up-Kicker not working or weak</option>
                                    <option value="Sound distorted" <%= (typeof issue !== 'undefined' && issue && issue.issue_type === 'Sound distorted') ? 'selected' : '' %>>Sound distorted</option>
                                </select>
                                <div class="form-text">What type of issue are you experiencing?</div>
                            </div>

                            <div class="mb-3">
                                <label for="priority" class="form-label">How urgent is this? *</label>
                                <select class="form-select" id="priority" name="priority" required>
                                    <option value="">Select urgency...</option>
                                    <option value="Low" <%= (typeof issue !== 'undefined' && issue && issue.priority === 'Low') ? 'selected' : '' %>>Low - Minor issue, machine still playable</option>
                                    <option value="Medium" <%= (typeof issue !== 'undefined' && issue && issue.priority === 'Medium') ? 'selected' : '' %>>Medium - Affects gameplay experience</option>
                                    <option value="High" <%= (typeof issue !== 'undefined' && issue && issue.priority === 'High') ? 'selected' : '' %>>High - Machine is unplayable</option>
                                </select>
                                <div class="form-text">Help us prioritize the repair</div>
                            </div>

                            <div class="mb-4">
                                <label for="description" class="form-label">Additional Details (Optional)</label>
                                <textarea class="form-control"
                                          id="description"
                                          name="description"
                                          rows="3"
                                          placeholder="Any additional details about the issue? When did it start? What were you doing when it happened?"><%= (typeof issue !== 'undefined' && issue && issue.description) ? issue.description : '' %></textarea>
                                <div class="form-text">Optional: Help us understand the problem better</div>
                            </div>

                            <!-- Photo Section -->
                            <div class="mb-4">
                                <label class="form-label">Photo (Optional)</label>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <!-- File Upload -->
                                        <div id="file-upload-section" class="text-center">
                                            <label for="photo-upload" class="btn btn-primary">
                                                <i class="bi bi-camera"></i> Upload Photo
                                            </label>
                                            <input type="file" id="photo-upload" name="photo" accept="image/*" class="d-none">
                                            <div class="form-text mt-2">Take or choose a photo from your device</div>
                                        </div>

                                        <!-- Photo Preview -->
                                        <div id="photo-preview" class="d-none">
                                            <div class="text-center mb-3">
                                                <img id="photo-preview-img" class="border" style="border-radius: 0.375rem; max-width: 100%; height: auto; max-height: 300px;">
                                            </div>
                                            <div class="text-center">
                                                <button type="button" id="remove-photo" class="btn btn-outline-danger">
                                                    <i class="bi bi-trash"></i> Remove Photo
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-warning btn-lg">
                                    <i class="bi bi-exclamation-triangle"></i> Report Issue
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Footer Info -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i> 
                        Your report will be sent to the machine operator for prompt attention.
                    </small>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Photo Upload JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const photoUpload = document.getElementById('photo-upload');
            const photoPreview = document.getElementById('photo-preview');
            const photoPreviewImg = document.getElementById('photo-preview-img');
            const removePhotoBtn = document.getElementById('remove-photo');

            photoUpload.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        photoPreviewImg.src = e.target.result;
                        photoPreview.classList.remove('d-none');
                    };
                    reader.readAsDataURL(file);
                }
            });

            removePhotoBtn.addEventListener('click', function() {
                photoUpload.value = '';
                photoPreview.classList.add('d-none');
                photoPreviewImg.src = '';
            });
        });
    </script>
</body>
</html>
