<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        .search-result-item:hover,
        .search-result-item.active {
            background-color: #f8f9fa;
        }

        .search-result-item:last-child {
            border-bottom: none !important;
        }

        #search_results {
            border-top: none;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }

        #machine_search:focus + input + #search_results {
            border-color: #86b7fe;
        }
    </style>
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/profile">Profile</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-plus-circle"></i> Add Pinball Machine
                    </h1>
                    <a href="/<%= operator.username %>/admin/machines" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Machines
                    </a>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Machine Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Select a Machine from the Global Database</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/<%= operator.username %>/admin/machines"
                              data-search-url="/<%= operator.username %>/api/machines/search">
                            <div class="mb-4">
                                <label for="machine_search" class="form-label">Pinball Machine *</label>
                                <div class="position-relative">
                                    <input type="text"
                                           class="form-control"
                                           id="machine_search"
                                           placeholder="Type to search for a machine..."
                                           autocomplete="off">
                                    <input type="hidden" id="global_machine_id" name="global_machine_id" required>

                                    <!-- Search Results Dropdown -->
                                    <div id="search_results" class="position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm" style="display: none; max-height: 300px; overflow-y: auto; z-index: 1000;">
                                        <!-- Results will be populated by JavaScript -->
                                    </div>
                                </div>
                                <div class="form-text">
                                    Choose from our global database of pinball machines.<br>
                                    <strong>Don't see your machine in the list?
                                    <a href="/<%= operator.username %>/admin/machines/add-global" class="text-decoration-none">
                                        <i class="bi bi-plus-circle"></i> Click here to add it to our database
                                    </a></strong>
                                </div>
                            </div>

                            <!-- Machine Preview -->
                            <div id="machine-preview" class="mb-4" style="display: none;">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">Machine Details</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>Name:</strong> <span id="preview-name"></span></p>
                                                <p class="mb-1"><strong>Manufacturer:</strong> <span id="preview-manufacturer"></span></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>Year:</strong> <span id="preview-year"></span></p>
                                                <p class="mb-1"><strong>Type:</strong> <span id="preview-type"></span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="location_notes" class="form-label">Location Notes</label>
                                <textarea class="form-control" 
                                          id="location_notes" 
                                          name="location_notes" 
                                          rows="3"
                                          placeholder="e.g., Main floor near entrance, Upstairs arcade section, etc."><%= machine ? (machine.location_notes || '') : '' %></textarea>
                                <div class="form-text">
                                    Optional notes about where this machine is located in your venue
                                </div>
                            </div>

                            <!-- QR Code Information -->
                            <div class="card bg-info bg-opacity-10 border-info mb-4">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="bi bi-qr-code"></i> QR Code Generation
                                    </h6>
                                    <p class="mb-0">
                                        When you add this machine, a unique QR code will be automatically generated. 
                                        This QR code can be printed and placed on the machine so customers can easily 
                                        report issues by scanning it with their phone.
                                    </p>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="/<%= operator.username %>/admin/machines" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Add Machine & Generate QR Code
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
    <!-- Machine Search JS -->
    <script src="/static/js/machine-search.js"></script>
</body>
</html>
