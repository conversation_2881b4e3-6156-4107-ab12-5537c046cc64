<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        .timeline-content h6 {
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        .timeline-content p {
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/<%= tech.operator_username %>/tech/dashboard">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-gear"></i> <%= tech.name %>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <form method="POST" action="/<%= tech.operator_username %>/tech/logout" class="d-inline">
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right"></i> Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-bug"></i> Issue #<%= issue.id %>
                    </h1>
                    <div>
                        <a href="/<%= tech.operator_username %>/tech/dashboard" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to My Issues
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <% if (typeof success !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <%= success %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Issue Details -->
        <div class="row">
            <!-- Main Issue Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Issue Details</h5>
                    </div>
                    <div class="card-body">
                        <!-- Issue Type and Priority -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6 class="text-muted">Issue Type</h6>
                                <h4><%= issue.issue_type %></h4>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Priority</h6>
                                <%
                                let priorityClass = 'bg-secondary';
                                if (issue.priority === 'High') priorityClass = 'bg-danger';
                                else if (issue.priority === 'Medium') priorityClass = 'bg-warning';
                                else if (issue.priority === 'Low') priorityClass = 'bg-success';
                                %>
                                <span class="badge <%= priorityClass %> fs-6"><%= issue.priority %></span>
                            </div>
                        </div>

                        <!-- Description -->
                        <% if (issue.user_comment) { %>
                            <div class="mb-3">
                                <h6 class="text-muted">Description</h6>
                                <p class="mb-0"><%= issue.user_comment %></p>
                            </div>
                        <% } %>

                        <!-- Photo -->
                        <% if (issue.user_picture_path) { %>
                            <div class="mb-3">
                                <h6 class="text-muted">Photo</h6>
                                <div class="photo-preview-container">
                                    <img src="<%= issue.user_picture_path %>"
                                         alt="Issue Photo"
                                         class="issue-photo-thumbnail img-thumbnail"
                                         style="max-width: 200px; max-height: 150px; cursor: pointer;"
                                         data-bs-toggle="modal"
                                         data-bs-target="#photoModal">
                                    <div class="mt-1">
                                        <small class="text-muted">
                                            <i class="bi bi-zoom-in"></i> Click to enlarge
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <% } %>

                        <!-- Status and Dates -->
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-muted">Status</h6>
                                <%
                                let statusClass = 'bg-secondary';
                                if (issue.status === 'Open') statusClass = 'bg-danger';
                                else if (issue.status === 'Assigned') statusClass = 'bg-warning';
                                else if (issue.status === 'Resolved') statusClass = 'bg-success';
                                else if (issue.status === 'Not Duplicated') statusClass = 'bg-info';
                                %>
                                <span class="badge <%= statusClass %> fs-6"><%= issue.status %></span>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-muted">Created</h6>
                                <p class="mb-0"><%= issue.formatted_created_date %></p>
                                <small class="text-muted"><%= issue.formatted_created_time %></small>
                            </div>
                            <div class="col-md-4">
                                <% if (issue.resolved_at) { %>
                                    <h6 class="text-muted">Resolved</h6>
                                    <p class="mb-0"><%= issue.formatted_resolved_date %></p>
                                    <small class="text-muted"><%= issue.formatted_resolved_time %></small>
                                <% } else { %>
                                    <h6 class="text-muted">Resolved</h6>
                                    <p class="mb-0 text-muted">Not yet resolved</p>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Comments -->
                <% if (issue.tech_comment) { %>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-chat-left-text"></i>
                                Comment made by
                                <% if (issue.comment_tech_name) { %>
                                    <%= issue.comment_tech_name %> (Technician)
                                <% } else if (issue.comment_operator_name) { %>
                                    <%= issue.comment_operator_name %> (Operator)
                                <% } else { %>
                                    Unknown
                                <% } %>
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0"><%= issue.tech_comment %></p>
                        </div>
                    </div>
                <% } %>

                <!-- Action Buttons for Tech -->
                <% if (issue.status === 'Assigned') { %>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-success w-100 mb-2" data-bs-toggle="modal" data-bs-target="#resolveModal">
                                        <i class="bi bi-check-circle"></i> Mark as Resolved
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-warning w-100 mb-2" data-bs-toggle="modal" data-bs-target="#notDuplicatedModal">
                                        <i class="bi bi-x-circle"></i> Mark as Not Duplicated
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <% } %>

                <!-- Recently Not Duplicated Issues -->
                <% if (notDuplicatedIssues && notDuplicatedIssues.length > 0) { %>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle text-warning"></i>
                                Recently Not Duplicated Issues
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">
                                Similar issues on this machine that were marked as "Not Duplicated".
                                This may indicate a recurring problem.
                            </p>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Issue Type</th>
                                            <th>Assigned To</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% notDuplicatedIssues.forEach(notDupIssue => { %>
                                            <tr>
                                                <td>
                                                    <% if (notDupIssue.assigned_tech_id === tech.id) { %>
                                                        <a href="/<%= tech.operator_username %>/tech/issues/<%= notDupIssue.id %>"
                                                           class="text-decoration-none">
                                                            <strong>#<%= notDupIssue.id %></strong>
                                                        </a>
                                                    <% } else { %>
                                                        <strong>#<%= notDupIssue.id %></strong>
                                                    <% } %>
                                                </td>
                                                <td><%= notDupIssue.issue_type %></td>
                                                <td>
                                                    <% if (notDupIssue.assigned_tech_id || notDupIssue.assigned_at) { %>
                                                        <% if (notDupIssue.assigned_tech_id && notDupIssue.tech_name) { %>
                                                            <span class="text-info">
                                                                <i class="bi bi-person"></i> <%= notDupIssue.tech_name %>
                                                            </span>
                                                        <% } else { %>
                                                            <span class="text-primary">
                                                                <i class="bi bi-buildings"></i> <%= tech.operator_name %>
                                                            </span>
                                                        <% } %>
                                                    <% } else { %>
                                                        <span class="text-muted">Not assigned</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <span class="text-muted"><%= notDupIssue.formatted_created_date %></span>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Machine Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Machine Information</h6>
                    </div>
                    <div class="card-body">
                        <p><strong><%= issue.machine_name %></strong></p>
                        <% if (issue.manufacturer) { %>
                            <p class="mb-1">
                                <small class="text-muted">Manufacturer:</small><br>
                                <%= issue.manufacturer %>
                            </p>
                        <% } %>
                        <% if (issue.machine_type) { %>
                            <p class="mb-1">
                                <small class="text-muted">Type:</small><br>
                                <%= issue.machine_type %>
                            </p>
                        <% } %>
                        <% if (issue.location_notes) { %>
                            <p class="mb-1">
                                <small class="text-muted">Location:</small><br>
                                <i class="bi bi-geo-alt"></i> <%= issue.location_notes %>
                            </p>
                        <% } %>
                        <p class="mb-0">
                            <small class="text-muted">Status:</small><br>
                            <% if (issue.machine_status === 'active') { %>
                                <span class="badge bg-success">Active</span>
                            <% } else { %>
                                <span class="badge bg-secondary">Inactive</span>
                            <% } %>
                        </p>
                    </div>
                </div>

                <!-- Assignment Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Assignment Information</h6>
                    </div>
                    <div class="card-body">
                        <h6 class="text-success">
                            <i class="bi bi-person-check"></i> Assigned to You
                        </h6>
                        <p class="mb-1"><strong><%= tech.name %></strong> (Technician)</p>
                        <% if (tech.email) { %>
                            <p class="mb-2">
                                <a href="mailto:<%= tech.email %>" class="text-decoration-none">
                                    <i class="bi bi-envelope"></i> <%= tech.email %>
                                </a>
                            </p>
                        <% } %>
                        <% if (issue.assigned_at) { %>
                            <small class="text-muted">
                                Assigned on <%= issue.formatted_assigned_date %>
                            </small>
                        <% } %>
                    </div>
                </div>

                <!-- Issue Timeline -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Issue Timeline</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Issue Created</h6>
                                    <p class="mb-0 text-muted">
                                        <%= issue.formatted_created_date %><br>
                                        <small><%= issue.formatted_created_time %></small>
                                    </p>
                                </div>
                            </div>

                            <% if (issue.assigned_at) { %>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Assigned to You</h6>
                                        <p class="mb-0 text-muted">
                                            <%= issue.formatted_assigned_date %><br>
                                            <small><%= issue.formatted_assigned_time %></small>
                                        </p>
                                    </div>
                                </div>
                            <% } %>

                            <% if (issue.resolved_at) { %>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Resolved</h6>
                                        <p class="mb-0 text-muted">
                                            <%= issue.formatted_resolved_date %><br>
                                            <small><%= issue.formatted_resolved_time %></small>
                                        </p>
                                    </div>
                                </div>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Resolve Issue Modal -->
    <div class="modal fade" id="resolveModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle text-success"></i> Mark Issue as Resolved
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="/<%= tech.operator_username %>/tech/issues/<%= issue.id %>/resolve">
                    <div class="modal-body">
                        <p>Are you sure you want to mark this issue as resolved?</p>
                        <div class="mb-3">
                            <label for="tech_comments_resolve" class="form-label">Tech Comments (Optional)</label>
                            <textarea class="form-control" id="tech_comments_resolve" name="tech_comments" rows="3"
                                      placeholder="Add any comments about the resolution..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> Mark as Resolved
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Not Duplicated Modal -->
    <div class="modal fade" id="notDuplicatedModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-x-circle text-warning"></i> Mark Issue as Not Duplicated
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="/<%= tech.operator_username %>/tech/issues/<%= issue.id %>/not-duplicated">
                    <div class="modal-body">
                        <p>Are you sure you want to mark this issue as not duplicated?</p>
                        <div class="mb-3">
                            <label for="tech_comments_not_dup" class="form-label">Tech Comments (Optional)</label>
                            <textarea class="form-control" id="tech_comments_not_dup" name="tech_comments" rows="3"
                                      placeholder="Add any comments about why this is not a duplicate..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-x-circle"></i> Mark as Not Duplicated
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Photo Modal -->
    <% if (issue.user_picture_path) { %>
        <div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="photoModalLabel">
                            <i class="bi bi-image"></i> Issue Photo
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="<%= issue.user_picture_path %>"
                             alt="Issue Photo"
                             class="img-fluid"
                             style="max-width: 100%; height: auto;">
                    </div>
                    <div class="modal-footer">
                        <a href="<%= issue.user_picture_path %>"
                           download="issue-<%= issue.id %>-photo.jpg"
                           class="btn btn-primary">
                            <i class="bi bi-download"></i> Download
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    <% } %>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
</body>
</html>
