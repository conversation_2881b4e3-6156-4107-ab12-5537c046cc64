<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Error Messages -->
        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-person-<%= isEdit ? 'gear' : 'plus' %>"></i> 
                        <%= isEdit ? 'Edit Tech' : 'Add New Tech' %>
                    </h1>
                    <a href="/<%= operator.username %>/admin/techs" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Techs
                    </a>
                </div>
            </div>
        </div>

        <!-- Tech Form -->
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <% if (isEdit) { %>
                                Edit Tech Information
                            <% } else { %>
                                Tech Information
                            <% } %>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="<%= isEdit ? `/${operator.username}/admin/techs/${tech.id}/edit` : `/${operator.username}/admin/techs` %>">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<%= tech ? tech.name : '' %>" required>
                                <div class="form-text">The technician's full name</div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<%= tech ? tech.email : '' %>" required>
                                <div class="form-text">Used for notifications and communication</div>
                            </div>

                            <div class="mb-3">
                                <label for="username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<%= tech ? tech.username : '' %>" required>
                                <div class="form-text">Used for logging into the system</div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    Password <% if (isEdit) { %>(leave blank to keep current)<% } else { %>*<% } %>
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       <%= !isEdit ? 'required' : '' %>>
                                <div class="form-text">
                                    <% if (isEdit) { %>
                                        Only enter a password if you want to change it
                                    <% } else { %>
                                        Minimum 6 characters recommended
                                    <% } %>
                                </div>
                            </div>

                            <% if (isEdit) { %>
                                <div class="mb-3">
                                    <label class="form-label">Current Status</label>
                                    <div>
                                        <% if (tech.status === 'active') { %>
                                            <span class="badge bg-success">Active</span>
                                        <% } else { %>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <% } %>
                                    </div>
                                    <div class="form-text">Use the Activate/Deactivate buttons in the tech list to change status</div>
                                </div>
                            <% } %>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/<%= operator.username %>/admin/techs" class="btn btn-secondary me-md-2">
                                    Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-<%= isEdit ? 'check' : 'plus' %>-circle"></i>
                                    <%= isEdit ? 'Update Tech' : 'Add Tech' %>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <% if (isEdit) { %>
                    <!-- Additional Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Additional Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <% if (tech.status === 'active') { %>
                                        <form method="POST" action="/<%= operator.username %>/admin/techs/<%= tech.id %>/deactivate" class="d-inline">
                                            <button type="submit" class="btn btn-warning w-100" 
                                                    onclick="return confirm('Are you sure you want to deactivate this tech? They will no longer be able to log in.')">
                                                <i class="bi bi-pause-circle"></i> Deactivate Tech
                                            </button>
                                        </form>
                                    <% } else { %>
                                        <form method="POST" action="/<%= operator.username %>/admin/techs/<%= tech.id %>/reactivate" class="d-inline">
                                            <button type="submit" class="btn btn-success w-100" 
                                                    onclick="return confirm('Are you sure you want to reactivate this tech?')">
                                                <i class="bi bi-play-circle"></i> Reactivate Tech
                                            </button>
                                        </form>
                                    <% } %>
                                </div>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
</body>
</html>
