const express = require('express');
const { body, validationResult } = require('express-validator');
const { 
  authenticateSuperAdmin, 
  authenticateOperator, 
  authenticateTech 
} = require('../middleware/auth');
const database = require('../models/database');

const router = express.Router();

// Super Admin Login Routes
router.get('/super-admin-login', (req, res) => {
  res.render('auth/super-admin-login', { 
    title: 'Super Admin Login',
    error: null 
  });
});

router.post('/super-admin-login', [
  body('username').notEmpty().withMessage('Username is required'),
  body('password').notEmpty().withMessage('Password is required')
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.render('auth/super-admin-login', {
      title: 'Super Admin Login',
      error: 'Please fill in all fields'
    });
  }

  const { username, password } = req.body;
  
  try {
    const admin = await authenticateSuperAdmin(username, password);
    
    if (!admin) {
      return res.render('auth/super-admin-login', {
        title: 'Super Admin Login',
        error: 'Invalid username or password'
      });
    }
    
    req.session.superAdmin = {
      id: admin.id,
      username: admin.username
    };
    
    res.redirect('/admin');
  } catch (error) {
    console.error('Super admin login error:', error);
    res.render('auth/super-admin-login', {
      title: 'Super Admin Login',
      error: 'An error occurred during login'
    });
  }
});

// Operator Login Routes
router.get('/login', (req, res) => {
  res.render('auth/login', {
    title: 'Login',
    error: null,
    operatorAccount: null
  });
});

// Handle the general login form submission
router.post('/login', [
  body('operatorAccount').notEmpty().withMessage('Operator account is required')
], async (req, res) => {
  const { operatorAccount } = req.body;

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.render('auth/login', {
      title: 'Login',
      error: 'Please enter an operator account name',
      operatorAccount: null
    });
  }

  // Redirect to the specific operator login page
  res.redirect(`/${operatorAccount}/login`);
});

// General operator login page
router.get('/operator-login', (req, res) => {
  res.render('auth/operator-login', {
    title: 'Operator Login',
    error: null,
    operator: null
  });
});

// General operator login POST
router.post('/operator-login', [
  body('username').notEmpty().withMessage('Username is required'),
  body('password').notEmpty().withMessage('Password is required')
], async (req, res) => {
  const { username, password } = req.body;

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.render('auth/operator-login', {
      title: 'Operator Login',
      error: 'Please fill in all fields',
      operator: null
    });
  }

  try {
    const user = await authenticateOperator(username, password);
    if (user) {
      req.session.operator = {
        id: user.id,
        username: user.username,
        name: user.name
      };
      return res.redirect(`/${user.username}/admin`);
    }

    res.render('auth/operator-login', {
      title: 'Operator Login',
      error: 'Invalid username or password',
      operator: null
    });

  } catch (error) {
    console.error('Operator login error:', error);
    res.render('auth/operator-login', {
      title: 'Operator Login',
      error: 'An error occurred during login',
      operator: null
    });
  }
});



// Logout routes
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Logout error:', err);
    }
    res.redirect('/');
  });
});

router.post('/:operatorAccount/logout', (req, res) => {
  const { operatorAccount } = req.params;
  req.session.destroy((err) => {
    if (err) {
      console.error('Logout error:', err);
    }
    res.redirect(`/${operatorAccount}`);
  });
});



module.exports = router;
