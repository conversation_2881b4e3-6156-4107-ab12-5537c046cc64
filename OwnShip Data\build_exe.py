"""
Build script to create standalone executable for Own Ship Extractor
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("PyInstaller is already installed")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

def create_spec_file():
    """Create PyInstaller spec file with custom configuration"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['own_ship_extractor.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('MercatorTools.cs', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'matplotlib.backends.backend_tkagg',
        'pandas',
        'sqlite3',
        'datetime',
        'math',
        'threading',
        'tkcalendar',
        'database_manager',
        'data_extractor',
        'cli_interface'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='OwnShipExtractor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for GUI app, True for console app
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon file path here if you have one
)
'''
    
    with open('OwnShipExtractor.spec', 'w') as f:
        f.write(spec_content.strip())
    
    print("Created OwnShipExtractor.spec file")

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable...")
    
    # Clean previous builds
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    
    # Build using spec file
    cmd = [sys.executable, "-m", "PyInstaller", "--clean", "OwnShipExtractor.spec"]
    
    try:
        subprocess.check_call(cmd)
        print("\n✅ Build completed successfully!")
        print(f"Executable created: {os.path.abspath('dist/OwnShipExtractor.exe')}")
        
        # Check file size
        exe_path = Path('dist/OwnShipExtractor.exe')
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"File size: {size_mb:.1f} MB")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed with error: {e}")
        return False
    
    return True

def create_distribution_package():
    """Create a distribution package with the executable and documentation"""
    if not os.path.exists('dist/OwnShipExtractor.exe'):
        print("❌ Executable not found. Build failed.")
        return
    
    # Create distribution folder
    dist_folder = 'OwnShipExtractor_Distribution'
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    
    os.makedirs(dist_folder)
    
    # Copy executable
    shutil.copy2('dist/OwnShipExtractor.exe', dist_folder)
    
    # Copy documentation and batch file
    files_to_copy = [
        'README.md',
        'extract_ownship_data.bat'
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, dist_folder)
    
    # Create usage instructions
    usage_content = """
# Own Ship Extractor - Standalone Distribution

## Files Included:
- OwnShipExtractor.exe - Main application (GUI and CLI)
- README.md - Complete documentation
- extract_ownship_data.bat - Batch file for Windows Task Scheduler

## Quick Start:

### GUI Mode:
Double-click OwnShipExtractor.exe to open the graphical interface.

### Command Line Mode:
Open Command Prompt in this folder and run:
OwnShipExtractor.exe "path\\to\\OwnShipRecorder.tzdb" --output "output_folder" --hours 24

### Automated Mode (Windows Task Scheduler):
Use extract_ownship_data.bat with Windows Task Scheduler for automatic data extraction.

## System Requirements:
- Windows 7 or later
- No Python installation required
- TimeZero software with OwnShip Recorder data

For complete documentation, see README.md
"""
    
    with open(os.path.join(dist_folder, 'USAGE.txt'), 'w') as f:
        f.write(usage_content.strip())
    
    print(f"\n📦 Distribution package created: {os.path.abspath(dist_folder)}")
    print("Ready for distribution!")

def main():
    """Main build process"""
    print("Own Ship Extractor - Executable Builder")
    print("=" * 40)
    
    # Check if we're in the right directory
    required_files = ['own_ship_extractor.py', 'database_manager.py', 'data_extractor.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        print("Please run this script from the Own Ship Extractor directory.")
        return
    
    # Install PyInstaller
    install_pyinstaller()
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if build_executable():
        create_distribution_package()
    
    print("\nBuild process completed!")

if __name__ == "__main__":
    main()
