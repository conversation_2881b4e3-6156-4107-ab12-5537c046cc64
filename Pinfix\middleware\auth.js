const bcrypt = require('bcryptjs');
const database = require('../models/database');

// Authentication middleware for super admin
const requireSuperAdmin = async (req, res, next) => {
  if (!req.session.superAdmin) {
    return res.redirect('/auth/super-admin-login');
  }
  next();
};

// Authentication middleware for operators
const requireOperator = async (req, res, next) => {
  if (!req.session.operator) {
    const operatorAccount = req.params.operatorAccount || req.body.operatorAccount;
    if (operatorAccount) {
      return res.redirect(`/${operatorAccount}/login`);
    }
    return res.redirect('/auth/login');
  }
  next();
};

// Authentication middleware for operator admin pages
const requireOperatorAuth = async (req, res, next) => {
  if (!req.session.operator) {
    const operatorAccount = req.params.operatorAccount;
    if (operatorAccount) {
      return res.redirect(`/auth/operator-login?redirect=/${operatorAccount}/admin`);
    }
    return res.redirect('/auth/operator-login');
  }

  // Verify the logged-in operator matches the requested operator account
  if (req.operatorContext && req.session.operator.id !== req.operatorContext.id) {
    return res.status(403).render('error', {
      title: 'Access Denied',
      message: 'You do not have permission to access this operator account.',
      error: {}
    });
  }

  next();
};

// Authentication middleware for techs
const requireTech = async (req, res, next) => {
  if (!req.session.tech && !req.session.operator) {
    const operatorAccount = req.params.operatorAccount || req.body.operatorAccount;
    if (operatorAccount) {
      return res.redirect(`/${operatorAccount}/login`);
    }
    return res.redirect('/auth/login');
  }
  next();
};

// Middleware to load operator context
const loadOperatorContext = async (req, res, next) => {
  const operatorAccount = req.params.operatorAccount;

  if (operatorAccount) {
    try {
      const operator = await database.get(
        'SELECT * FROM operators WHERE username = ? AND status = "active"',
        [operatorAccount]
      );

      if (!operator) {
        return res.status(404).render('error', {
          title: 'Operator Not Found',
          message: 'The operator account you are looking for does not exist or is disabled.',
          error: {}
        });
      }

      req.operatorContext = operator;
      res.locals.operatorContext = operator;
    } catch (error) {
      console.error('Error loading operator context:', error);
      return res.status(500).render('error', {
        title: 'Error',
        message: 'An error occurred while loading the operator information.',
        error: {}
      });
    }
  }

  next();
};

// Helper function to verify password
const verifyPassword = async (plainPassword, hashedPassword) => {
  return await bcrypt.compare(plainPassword, hashedPassword);
};

// Helper function to hash password
const hashPassword = async (password) => {
  return await bcrypt.hash(password, 10);
};

// Helper function to authenticate super admin
const authenticateSuperAdmin = async (username, password) => {
  try {
    const admin = await database.get(
      'SELECT * FROM super_admin WHERE username = ?',
      [username]
    );
    
    if (!admin) {
      return null;
    }
    
    const isValid = await verifyPassword(password, admin.password_hash);
    if (!isValid) {
      return null;
    }
    
    return admin;
  } catch (error) {
    console.error('Error authenticating super admin:', error);
    return null;
  }
};

// Helper function to authenticate operator
const authenticateOperator = async (username, password, operatorAccount = null) => {
  try {
    let query = 'SELECT * FROM operators WHERE username = ? AND status = "active"';
    let params = [username];

    if (operatorAccount) {
      query += ' AND username = ?';
      params.push(operatorAccount);
    }

    const operator = await database.get(query, params);

    if (!operator) {
      return null;
    }

    const isValid = await verifyPassword(password, operator.password_hash);
    if (!isValid) {
      return null;
    }

    return operator;
  } catch (error) {
    console.error('Error authenticating operator:', error);
    return null;
  }
};

// Helper function to authenticate tech
const authenticateTech = async (username, password, operatorId) => {
  try {
    const tech = await database.get(
      'SELECT * FROM techs WHERE username = ? AND operator_id = ? AND status = "active"',
      [username, operatorId]
    );
    
    if (!tech) {
      return null;
    }
    
    const isValid = await verifyPassword(password, tech.password_hash);
    if (!isValid) {
      return null;
    }
    
    return tech;
  } catch (error) {
    console.error('Error authenticating tech:', error);
    return null;
  }
};

module.exports = {
  requireSuperAdmin,
  requireOperator,
  requireOperatorAuth,
  requireTech,
  loadOperatorContext,
  verifyPassword,
  hashPassword,
  authenticateSuperAdmin,
  authenticateOperator,
  authenticateTech
};
