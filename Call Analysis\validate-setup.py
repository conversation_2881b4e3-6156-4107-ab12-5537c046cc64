#!/usr/bin/env python3
"""
Call Analysis Setup Validation Script
Validates that all required files and configurations are in place for deployment.
"""

import os
import json
import sys
from pathlib import Path

def check_file_exists(filepath, description):
    """Check if a file exists and return status."""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (NOT FOUND)")
        return False

def check_file_content(filepath, description, min_length=10):
    """Check if a file exists and has content."""
    if not os.path.exists(filepath):
        print(f"❌ {description}: {filepath} (NOT FOUND)")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if len(content) >= min_length:
                print(f"✅ {description}: {filepath} (has content)")
                return True
            else:
                print(f"⚠️  {description}: {filepath} (file exists but appears empty)")
                return False
    except Exception as e:
        print(f"❌ {description}: {filepath} (error reading: {e})")
        return False

def validate_json_file(filepath, description):
    """Validate that a file contains valid JSON."""
    if not os.path.exists(filepath):
        print(f"❌ {description}: {filepath} (NOT FOUND)")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            json.load(f)
        print(f"✅ {description}: {filepath} (valid JSON)")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ {description}: {filepath} (invalid JSON: {e})")
        return False
    except Exception as e:
        print(f"❌ {description}: {filepath} (error reading: {e})")
        return False

def check_docker_files():
    """Check Docker-related files."""
    print("\n🐳 Docker Configuration:")
    
    files_ok = True
    files_ok &= check_file_exists("Dockerfile", "Dockerfile")
    files_ok &= check_file_exists("docker-compose.yml", "Docker Compose file")
    files_ok &= check_file_exists(".dockerignore", "Docker ignore file")
    files_ok &= check_file_exists("requirements.txt", "Python requirements")
    
    return files_ok

def check_kubernetes_files():
    """Check Kubernetes manifest files."""
    print("\n☸️  Kubernetes Configuration:")
    
    k8s_dir = "k8s"
    if not os.path.exists(k8s_dir):
        print(f"❌ Kubernetes directory: {k8s_dir} (NOT FOUND)")
        return False
    
    files_ok = True
    k8s_files = [
        "pod.yaml",
        "deployment.yaml", 
        "service.yaml",
        "ingress.yaml",
        "configmap.yaml",
        "secret.yaml",
        "pvc.yaml"
    ]
    
    for filename in k8s_files:
        filepath = os.path.join(k8s_dir, filename)
        files_ok &= check_file_exists(filepath, f"K8s {filename}")
    
    return files_ok

def check_api_keys():
    """Check API key files."""
    print("\n🔑 API Keys:")
    
    files_ok = True
    files_ok &= check_file_content("Assembly_KEY.txt", "AssemblyAI API Key", 20)
    files_ok &= check_file_content("OpenAI_KEY.txt", "OpenAI API Key", 20)
    files_ok &= validate_json_file("rc-credentials.json", "RingCentral Credentials")
    
    return files_ok

def check_application_files():
    """Check main application files."""
    print("\n📱 Application Files:")
    
    files_ok = True
    files_ok &= check_file_exists("app.py", "Flask web application")
    files_ok &= check_file_exists("phone.py", "Core analysis module")
    files_ok &= check_file_exists("getReport.py", "RingCX API module")
    files_ok &= check_file_exists("rc_auth.py", "Authentication module")
    files_ok &= check_file_exists("templates/index.html", "Web interface template")
    
    return files_ok

def check_deployment_files():
    """Check deployment and documentation files."""
    print("\n📋 Deployment Files:")
    
    files_ok = True
    files_ok &= check_file_exists("deploy.sh", "Deployment script")
    files_ok &= check_file_exists("DEPLOYMENT.md", "Deployment guide")
    files_ok &= check_file_exists("README-DOCKER.md", "Docker README")
    
    # Check if deploy.sh is executable
    if os.path.exists("deploy.sh"):
        if os.access("deploy.sh", os.X_OK):
            print("✅ deploy.sh is executable")
        else:
            print("⚠️  deploy.sh exists but is not executable (run: chmod +x deploy.sh)")
            files_ok = False
    
    return files_ok

def check_python_imports():
    """Check if required Python modules can be imported."""
    print("\n🐍 Python Dependencies:")
    
    required_modules = [
        "flask",
        "flask_cors", 
        "requests",
        "pandas",
        "numpy",
        "reportlab",
        "PyJWT",
        "cryptography"
    ]
    
    imports_ok = True
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} (not installed)")
            imports_ok = False
    
    if not imports_ok:
        print("\n💡 Install missing dependencies with: pip install -r requirements.txt")
    
    return imports_ok

def main():
    """Main validation function."""
    print("🔍 Call Analysis Deployment Validation")
    print("=" * 50)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Run all checks
    checks = [
        ("API Keys", check_api_keys),
        ("Application Files", check_application_files),
        ("Docker Configuration", check_docker_files),
        ("Kubernetes Configuration", check_kubernetes_files),
        ("Deployment Files", check_deployment_files),
        ("Python Dependencies", check_python_imports)
    ]
    
    all_passed = True
    results = {}
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
            all_passed &= result
        except Exception as e:
            print(f"❌ Error during {check_name} check: {e}")
            results[check_name] = False
            all_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Validation Summary:")
    
    for check_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {check_name}")
    
    if all_passed:
        print("\n🎉 All checks passed! You're ready to deploy.")
        print("\nNext steps:")
        print("  1. Deploy with Docker: ./deploy.sh docker")
        print("  2. Deploy to Kubernetes: ./deploy.sh k8s")
        print("  3. Read DEPLOYMENT.md for detailed instructions")
        return 0
    else:
        print("\n⚠️  Some checks failed. Please fix the issues above before deploying.")
        print("\nCommon fixes:")
        print("  - Create missing API key files")
        print("  - Install Python dependencies: pip install -r requirements.txt")
        print("  - Make deploy.sh executable: chmod +x deploy.sh")
        return 1

if __name__ == "__main__":
    sys.exit(main())
