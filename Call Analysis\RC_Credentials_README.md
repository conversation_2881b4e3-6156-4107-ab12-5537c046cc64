# RingCentral Authentication with rc-credentials.json

This guide explains how to authenticate with RingCentral and RingCX APIs using the `rc-credentials.json` file.

## Overview

The authentication process uses JWT (JSON Web Token) to get RingCentral access tokens, which can then be used for:
1. **RingCentral API calls** (working)
2. **RingCX API calls** (requires additional permissions)

## Files

- `rc_auth.py` - Main authentication module
- `rc-credentials.json` - Your RingCentral credentials
- `example_usage.py` - Usage examples
- `RC_Credentials_README.md` - This documentation

## Authentication Flow

```
rc-credentials.json → JWT Token → RingCentral API → RC Access Token → RingCX API → RingCX Access Token
```

## Functions

### Core Functions

#### `get_ringcentral_access_token(credentials_file="rc-credentials.json")`

**Simple function to get RingCentral access token.**

```python
from rc_auth import get_ringcentral_access_token

# Get token directly
token = get_ringcentral_access_token()
print(f"Token: {token}")
```

#### `get_ringcentral_access_token_from_rc_credentials(credentials_file="rc-credentials.json")`

**Get full RingCentral authentication response.**

```python
from rc_auth import get_ringcentral_access_token_from_rc_credentials, extract_access_token

# Get full response
response = get_ringcentral_access_token_from_rc_credentials()
print(f"Expires in: {response['expires_in']} seconds")

# Extract token
token = extract_access_token(response)
```

#### `get_ringcx_access_token_with_rc_token(rc_access_token, login_url=None)`

**Get RingCX access token using RingCentral token.**

```python
from rc_auth import get_ringcentral_access_token, get_ringcx_access_token_with_rc_token

# Get RC token first
rc_token = get_ringcentral_access_token()

# Get RingCX token (requires permissions)
ringcx_response = get_ringcx_access_token_with_rc_token(rc_token)
```

#### `get_ringcx_access_token_from_rc_credentials(credentials_file="rc-credentials.json")`

**Complete flow: RC credentials → RingCX token.**

```python
from rc_auth import get_ringcx_access_token_from_rc_credentials

# Complete authentication flow
ringcx_response = get_ringcx_access_token_from_rc_credentials()
```

## Configuration

### rc-credentials.json Structure

Your `rc-credentials.json` file should have this structure:

```json
{
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret", 
    "server": "https://platform.ringcentral.com",
    "jwt": {
        "timezero": "your_jwt_token_here"
    }
}
```

## Usage Examples

### Basic RingCentral Authentication

```python
from rc_auth import get_ringcentral_access_token

try:
    # Get RingCentral access token
    token = get_ringcentral_access_token()
    print(f"Success: {token[:50]}...")
    
    # Use for API calls
    headers = {"Authorization": f"Bearer {token}"}
    
except RuntimeError as e:
    print(f"Authentication failed: {e}")
```

### RingCentral API Call Example

```python
import requests
from rc_auth import get_ringcentral_access_token

# Get token
token = get_ringcentral_access_token()

# Make API call
headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json"
}

response = requests.get(
    "https://platform.ringcentral.com/restapi/v1.0/account/~/extension/~",
    headers=headers
)

if response.status_code == 200:
    user_info = response.json()
    print(f"User: {user_info.get('name')}")
```

### RingCX Authentication (Requires Permissions)

```python
from rc_auth import get_ringcx_access_token_from_rc_credentials, extract_access_token

try:
    # Complete RingCX authentication
    response = get_ringcx_access_token_from_rc_credentials()
    token = extract_access_token(response)
    
    print(f"RingCX token: {token[:50]}...")
    
except RuntimeError as e:
    print(f"RingCX authentication failed: {e}")
    # This may fail if application lacks ReadAccounts permission
```

## Testing

### Run the Test Script

```bash
python rc_auth.py
```

Expected output for working RingCentral authentication:
```
✓ Credentials loaded successfully
✓ RingCentral authentication successful
✗ RingCX authentication failed (permission error)
```

### Run Usage Examples

```bash
python example_usage.py
```

## Current Status

### ✅ Working Features

1. **RingCentral Authentication**: Fully working
   - Reads credentials from `rc-credentials.json`
   - Uses JWT for authentication
   - Returns valid RingCentral access tokens
   - Tokens can be used for RingCentral API calls

### ⚠️ Permission Issues

2. **RingCX Authentication**: Requires additional setup
   - RingCentral authentication works
   - RingCX step fails with: `"application needs to have [ReadAccounts] permission"`
   - This is a RingCentral application configuration issue

## Troubleshooting

### RingCentral Authentication Issues

1. **"JWT signature verification failed"**
   - Check if JWT token in `rc-credentials.json` is valid
   - Ensure JWT hasn't expired

2. **"Client authentication is required"**
   - Verify `clientId` and `clientSecret` are correct
   - Check credentials format in JSON file

3. **"Invalid client credentials"**
   - Confirm application is active in RingCentral Developer Portal
   - Verify client ID and secret match your application

### RingCX Permission Issues

1. **"application needs to have [ReadAccounts] permission"**
   - Log into [RingCentral Developer Portal](https://developers.ringcentral.com)
   - Go to your application settings
   - Add `ReadAccounts` permission
   - May require application review/approval

2. **Contact RingCentral Support**
   - If you need RingCX access, contact RingCentral support
   - Explain you need RingCX API access for your application

## API Endpoints Used

### RingCentral Token Endpoint
```
POST https://platform.ringcentral.com/restapi/oauth/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {base64(clientId:clientSecret)}

grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion={jwt_token}
```

### RingCX Login Endpoint
```
POST https://ringcx.ringcentral.com/api/auth/login/rc/accesstoken
Content-Type: application/x-www-form-urlencoded

rcAccessToken={rc_access_token}&rcTokenType=Bearer
```

## Security Notes

1. **Protect Credentials**: Keep `rc-credentials.json` secure
2. **Don't Commit**: Never commit credentials to version control
3. **Token Expiration**: RingCentral tokens expire (check `expires_in`)
4. **Refresh Tokens**: Use refresh tokens for long-running applications

## Next Steps

1. **For RingCentral API**: You can start using the tokens immediately
2. **For RingCX API**: Contact RingCentral to get `ReadAccounts` permission
3. **Integration**: Use the working authentication in your applications

The RingCentral authentication is fully functional and ready to use!
