// Machine search functionality for Add Machine form
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('machine_search');
    const hiddenInput = document.getElementById('global_machine_id');
    const searchResults = document.getElementById('search_results');
    const preview = document.getElementById('machine-preview');

    let selectedMachine = null;
    let searchTimeout = null;

    // Get search URL from form data attribute
    const form = document.querySelector('form[data-search-url]');
    const searchUrl = form ? form.getAttribute('data-search-url') : null;

    if (!searchUrl) {
        console.error('Search URL not found');
        return;
    }
    
    // Search input event listener
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            if (query.length < 2) {
                hideResults();
                clearSelection();
                return;
            }

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Debounce search requests
            searchTimeout = setTimeout(() => {
                searchMachines(query);
            }, 300);
        });
        
        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                hideResults();
            }
        });
        
        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            const items = searchResults.querySelectorAll('.search-result-item');
            const activeItem = searchResults.querySelector('.search-result-item.active');
            
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (activeItem) {
                    activeItem.classList.remove('active');
                    const next = activeItem.nextElementSibling;
                    if (next) {
                        next.classList.add('active');
                        next.scrollIntoView({ block: 'nearest' });
                    }
                } else if (items.length > 0) {
                    items[0].classList.add('active');
                }
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if (activeItem) {
                    activeItem.classList.remove('active');
                    const prev = activeItem.previousElementSibling;
                    if (prev) {
                        prev.classList.add('active');
                        prev.scrollIntoView({ block: 'nearest' });
                    }
                }
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (activeItem) {
                    activeItem.click();
                }
            } else if (e.key === 'Escape') {
                hideResults();
            }
        });
    }
    
    function searchMachines(query) {
        // Show loading state
        searchResults.innerHTML = '<div class="p-3 text-muted text-center"><i class="bi bi-search"></i> Searching...</div>';
        showResults();

        // Make AJAX request
        fetch(`${searchUrl}?q=${encodeURIComponent(query)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Search request failed');
                }
                return response.json();
            })
            .then(machines => {
                displayResults(machines);
            })
            .catch(error => {
                console.error('Search error:', error);
                searchResults.innerHTML = '<div class="p-3 text-danger text-center"><i class="bi bi-exclamation-triangle"></i> Search failed. Please try again.</div>';
                showResults();
            });
    }
    
    function displayResults(filteredMachines) {
        if (filteredMachines.length === 0) {
            searchResults.innerHTML = '<div class="p-3 text-muted text-center">No machines found</div>';
        } else {
            const html = filteredMachines.map(machine => `
                <div class="search-result-item p-3 border-bottom cursor-pointer" 
                     data-machine-id="${machine.id}"
                     style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="fw-semibold">${machine.name}</div>
                            <small class="text-muted">
                                ${machine.manufacturer} • ${machine.year} • ${machine.type}
                            </small>
                        </div>
                    </div>
                </div>
            `).join('');
            
            searchResults.innerHTML = html;
            
            // Add click handlers to result items
            searchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', function() {
                    const machineId = this.getAttribute('data-machine-id');
                    selectMachine(machineId);
                });
                
                // Add hover effect
                item.addEventListener('mouseenter', function() {
                    searchResults.querySelectorAll('.search-result-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        }
        
        showResults();
    }
    
    function selectMachine(machineId) {
        // Find the machine from the current search results
        const machineElement = searchResults.querySelector(`[data-machine-id="${machineId}"]`);
        if (machineElement) {
            // Extract machine data from the element
            const machineName = machineElement.querySelector('.fw-semibold').textContent;
            const machineDetails = machineElement.querySelector('.text-muted').textContent;
            const [manufacturer, year, type] = machineDetails.split(' • ');

            selectedMachine = {
                id: machineId,
                name: machineName,
                manufacturer: manufacturer,
                year: year,
                type: type
            };

            searchInput.value = `${machineName} (${year}) - ${manufacturer}`;
            hiddenInput.value = machineId;
            hideResults();
            updatePreview(selectedMachine);
        }
    }
    
    function clearSelection() {
        selectedMachine = null;
        hiddenInput.value = '';
        hidePreview();
    }
    
    function showResults() {
        searchResults.style.display = 'block';
    }
    
    function hideResults() {
        searchResults.style.display = 'none';
        // Clear active states
        searchResults.querySelectorAll('.search-result-item').forEach(item => {
            item.classList.remove('active');
        });
    }
    
    function updatePreview(machine) {
        if (preview) {
            document.getElementById('preview-name').textContent = machine.name;
            document.getElementById('preview-manufacturer').textContent = machine.manufacturer;
            document.getElementById('preview-year').textContent = machine.year;
            document.getElementById('preview-type').textContent = machine.type;
            preview.style.display = 'block';
        }
    }
    
    function hidePreview() {
        if (preview) {
            preview.style.display = 'none';
        }
    }
    
    // Form validation
    const submitForm = document.querySelector('form');
    if (submitForm) {
        submitForm.addEventListener('submit', function(e) {
            const machineId = hiddenInput.value;
            
            if (!machineId) {
                e.preventDefault();
                alert('Please select a machine from the search results');
                searchInput.focus();
                return false;
            }
        });
    }
});
