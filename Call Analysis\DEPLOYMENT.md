# Call Analysis - Cloud Deployment Guide

This guide provides instructions for deploying the Call Analysis application on various cloud platforms using Docker and Kubernetes.

## Overview

The application has been containerized and includes:
- **Flask Web Application**: Replaces the tkinter GUI with a web interface
- **Docker Container**: For consistent deployment across environments
- **Kubernetes Manifests**: For scalable cloud deployment
- **Persistent Storage**: For database and reports
- **Secret Management**: For secure API key handling

## Prerequisites

### Required API Keys
1. **AssemblyAI API Key**: For audio transcription
2. **OpenAI API Key**: For ChatGPT analysis
3. **RingCentral Credentials**: JWT token and client credentials

### Required Tools
- Docker
- kubectl (for Kubernetes deployment)
- Access to a Kubernetes cluster (EKS, GKE, AKS, etc.)

## Quick Start with Docker Compose

### 1. Prepare API Keys
Create the following files in your project directory:

```bash
# AssemblyAI API Key
echo "your-assemblyai-api-key" > Assembly_KEY.txt

# OpenAI API Key  
echo "your-openai-api-key" > OpenAI_KEY.txt

# RingCentral credentials (copy your existing file)
cp your-existing-rc-credentials.json rc-credentials.json
```

### 2. Build and Run
```bash
# Build the Docker image
docker-compose build

# Start the application
docker-compose up -d

# Check logs
docker-compose logs -f call-analysis
```

### 3. Access the Application
Open your browser and navigate to: `http://localhost:5000`

## Kubernetes Deployment

### 1. Prepare Secrets
```bash
# Create namespace (optional)
kubectl create namespace call-analysis

# Create secrets with your API keys
kubectl create secret generic call-analysis-secrets \
  --from-literal=ASSEMBLYAI_API_KEY="your-assemblyai-key" \
  --from-literal=OPENAI_API_KEY="your-openai-key" \
  --from-file=RC_CREDENTIALS_JSON=rc-credentials.json \
  --namespace=call-analysis
```

### 2. Build and Push Docker Image
```bash
# Build the image
docker build -t your-registry/call-analysis:latest .

# Push to your container registry
docker push your-registry/call-analysis:latest
```

### 3. Update Deployment Configuration
Edit `k8s/deployment.yaml` and update the image reference:
```yaml
spec:
  containers:
  - name: call-analysis
    image: your-registry/call-analysis:latest  # Update this line
```

### 4. Deploy to Kubernetes
```bash
# Apply all manifests
kubectl apply -f k8s/ --namespace=call-analysis

# Check deployment status
kubectl get pods -n call-analysis
kubectl get services -n call-analysis
```

### 5. Access the Application

#### Option A: Port Forward (for testing)
```bash
kubectl port-forward service/call-analysis-service 5000:80 -n call-analysis
```
Then access: `http://localhost:5000`

#### Option B: LoadBalancer (cloud environments)
```bash
# Get external IP
kubectl get service call-analysis-loadbalancer -n call-analysis

# Access via external IP on port 80
```

#### Option C: Ingress (with domain)
1. Update `k8s/ingress.yaml` with your domain
2. Configure DNS to point to your ingress controller
3. Access via your domain

## Cloud Platform Specific Instructions

### Amazon EKS
```bash
# Create EKS cluster
eksctl create cluster --name call-analysis --region us-west-2

# Configure kubectl
aws eks update-kubeconfig --region us-west-2 --name call-analysis

# Deploy application
kubectl apply -f k8s/
```

### Google GKE
```bash
# Create GKE cluster
gcloud container clusters create call-analysis \
  --zone us-central1-a \
  --num-nodes 2

# Get credentials
gcloud container clusters get-credentials call-analysis --zone us-central1-a

# Deploy application
kubectl apply -f k8s/
```

### Azure AKS
```bash
# Create resource group
az group create --name call-analysis-rg --location eastus

# Create AKS cluster
az aks create \
  --resource-group call-analysis-rg \
  --name call-analysis \
  --node-count 2 \
  --enable-addons monitoring \
  --generate-ssh-keys

# Get credentials
az aks get-credentials --resource-group call-analysis-rg --name call-analysis

# Deploy application
kubectl apply -f k8s/
```

## Configuration

### Environment Variables
The application supports the following environment variables:

- `FLASK_ENV`: Flask environment (production/development)
- `DATABASE_PATH`: Path to SQLite database
- `REPORTS_PATH`: Path for generated reports
- `RINGCX_BASE_URL`: RingCX API base URL
- `DEFAULT_DAYS`: Default number of days for call queries

### Storage
- **Database**: SQLite database stored in persistent volume
- **Reports**: PDF reports stored in separate persistent volume
- **Logs**: Application logs available via `kubectl logs`

## Monitoring and Troubleshooting

### Health Checks
The application provides a health endpoint at `/health`

### Logs
```bash
# Docker Compose
docker-compose logs -f call-analysis

# Kubernetes
kubectl logs -f deployment/call-analysis -n call-analysis
```

### Common Issues

1. **API Key Issues**: Verify secrets are created correctly
2. **Storage Issues**: Check PVC status and storage class
3. **Network Issues**: Verify service and ingress configuration
4. **Resource Issues**: Check pod resource limits and node capacity

## Security Considerations

1. **API Keys**: Stored as Kubernetes secrets
2. **Network**: Use ingress with TLS termination
3. **Authentication**: Consider adding basic auth or OAuth
4. **Container Security**: Runs as non-root user
5. **Resource Limits**: CPU and memory limits configured

## Scaling

### Horizontal Scaling
```bash
# Scale deployment
kubectl scale deployment call-analysis --replicas=3 -n call-analysis
```

### Vertical Scaling
Update resource limits in `k8s/deployment.yaml`

## Backup and Recovery

### Database Backup
```bash
# Create backup
kubectl exec deployment/call-analysis -n call-analysis -- \
  sqlite3 /app/data/call_analysis.db ".backup /app/data/backup.db"

# Copy backup locally
kubectl cp call-analysis-namespace/pod-name:/app/data/backup.db ./backup.db
```

## Automated Deployment Script

Use the provided deployment script for easier setup:

```bash
# Make script executable
chmod +x deploy.sh

# Deploy to Docker Compose
./deploy.sh docker

# Deploy to Kubernetes
./deploy.sh k8s

# Clean up deployment
./deploy.sh clean
```

## Support

For issues and questions:
1. Check application logs
2. Verify API key configuration
3. Test network connectivity
4. Review Kubernetes events: `kubectl get events -n call-analysis`
