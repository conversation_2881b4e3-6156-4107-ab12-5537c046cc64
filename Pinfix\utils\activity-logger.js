const db = require('../models/database');

/**
 * Log activity for super admin dashboard
 * @param {string} activityType - Type of activity (issue_created, issue_resolved, etc.)
 * @param {Object} data - Activity data
 * @param {number} data.operatorId - ID of the operator
 * @param {number} data.machineId - ID of the machine (optional)
 * @param {number} data.issueId - ID of the issue (optional)
 * @param {number} data.techId - ID of the tech (optional)
 * @param {string} data.actorType - Type of actor (operator, tech, system, super_admin)
 * @param {string} data.actorName - Name of the actor
 * @param {string} data.description - Human-readable description
 */
async function logActivity(activityType, data) {
  try {
    await db.run(`
      INSERT INTO activity_log (
        activity_type, operator_id, machine_id, issue_id, tech_id,
        actor_type, actor_name, description, created_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `, [
      activityType,
      data.operatorId || null,
      data.machineId || null,
      data.issueId || null,
      data.techId || null,
      data.actorType,
      data.actorName,
      data.description
    ]);
  } catch (error) {
    console.error('Failed to log activity:', error);
    // Don't throw error to avoid breaking main functionality
  }
}

/**
 * Get recent activities for super admin dashboard
 * @param {number} limit - Number of activities to retrieve (default: 10)
 * @returns {Array} Recent activities with formatted data
 */
async function getRecentActivities(limit = 10) {
  try {
    const activities = await db.all(`
      SELECT
        al.*,
        o.name as operator_name,
        o.username as operator_username,
        gpm.name as machine_name,
        gpm.manufacturer as machine_manufacturer,
        t.name as tech_name
      FROM activity_log al
      LEFT JOIN operators o ON al.operator_id = o.id
      LEFT JOIN pinball_machines pm ON al.machine_id = pm.id
      LEFT JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      LEFT JOIN techs t ON al.tech_id = t.id
      ORDER BY al.created_at DESC
      LIMIT ?
    `, [limit]);

    return activities.map(activity => ({
      ...activity,
      formatted_time: formatActivityTime(activity.created_at),
      icon: getActivityIcon(activity.activity_type),
      color: getActivityColor(activity.activity_type)
    }));
  } catch (error) {
    console.error('Failed to get recent activities:', error);
    return [];
  }
}

/**
 * Format activity timestamp for display
 * @param {string} timestamp - ISO timestamp
 * @returns {string} Formatted time string
 */
function formatActivityTime(timestamp) {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString();
}

/**
 * Get Bootstrap icon for activity type
 * @param {string} activityType - Type of activity
 * @returns {string} Bootstrap icon class
 */
function getActivityIcon(activityType) {
  const icons = {
    'issue_created': 'bi-exclamation-triangle',
    'issue_assigned': 'bi-person-check',
    'issue_resolved': 'bi-check-circle',
    'issue_not_duplicated': 'bi-x-circle',
    'operator_created': 'bi-building-add',
    'operator_status_changed': 'bi-building-gear',
    'machine_added': 'bi-controller',
    'tech_added': 'bi-person-plus'
  };
  return icons[activityType] || 'bi-info-circle';
}

/**
 * Get color class for activity type
 * @param {string} activityType - Type of activity
 * @returns {string} Bootstrap color class
 */
function getActivityColor(activityType) {
  const colors = {
    'issue_created': 'text-warning',
    'issue_assigned': 'text-info',
    'issue_resolved': 'text-success',
    'issue_not_duplicated': 'text-secondary',
    'operator_created': 'text-primary',
    'operator_status_changed': 'text-primary',
    'machine_added': 'text-success',
    'tech_added': 'text-info'
  };
  return colors[activityType] || 'text-muted';
}

module.exports = {
  logActivity,
  getRecentActivities
};
