const express = require('express');
const router = express.Router();
const db = require('../models/database');
const { authenticateTech } = require('../middleware/auth');
const { logActivity } = require('../utils/activity-logger');

// Tech login routes
router.get('/:operatorAccount/tech/login', async (req, res) => {
  try {
    const { operatorAccount } = req.params;

    // Get operator info for the login page
    const operator = await db.get(
      'SELECT id, name, username FROM operators WHERE username = ?',
      [operatorAccount]
    );

    if (!operator) {
      return res.status(404).render('error', {
        title: 'Operator Not Found',
        message: 'The requested operator account does not exist.',
        error: { status: 404 }
      });
    }

    res.render('auth/tech-login', {
      title: 'Tech Login',
      operator,
      error: req.query.error
    });
  } catch (error) {
    console.error('Tech login page error:', error);
    res.status(500).render('error', {
      title: 'Server Error',
      message: 'An error occurred while loading the login page.',
      error: { status: 500 }
    });
  }
});

router.post('/:operatorAccount/tech/login', async (req, res) => {
  try {
    const { operatorAccount } = req.params;
    const { username, password } = req.body;

    // Get operator info
    const operator = await db.get(
      'SELECT id, name, username FROM operators WHERE username = ?',
      [operatorAccount]
    );

    if (!operator) {
      return res.redirect(`/${operatorAccount}/tech/login?error=Invalid operator account`);
    }

    // Authenticate tech
    const tech = await authenticateTech(username, password, operator.id);

    if (!tech) {
      return res.redirect(`/${operatorAccount}/tech/login?error=Invalid username or password`);
    }

    // Set session
    req.session.techId = tech.id;
    req.session.operatorId = operator.id;
    req.session.userType = 'tech';

    res.redirect(`/${operatorAccount}/tech/dashboard`);
  } catch (error) {
    console.error('Tech login error:', error);
    res.redirect(`/${req.params.operatorAccount}/tech/login?error=Login failed`);
  }
});

// Tech logout
router.post('/:operatorAccount/tech/logout', (req, res) => {
  const { operatorAccount } = req.params;
  req.session.destroy((err) => {
    if (err) {
      console.error('Tech logout error:', err);
    }
    res.redirect(`/${operatorAccount}/tech/login`);
  });
});

// Tech authentication middleware
const requireTechAuth = async (req, res, next) => {
  try {
    if (!req.session.techId || !req.session.operatorId || req.session.userType !== 'tech') {
      return res.redirect(`/${req.params.operatorAccount}/tech/login`);
    }

    // Get tech and operator info
    const tech = await db.get(`
      SELECT t.*, o.name as operator_name, o.username as operator_username
      FROM techs t
      JOIN operators o ON t.operator_id = o.id
      WHERE t.id = ? AND t.operator_id = ? AND t.status = 'active'
    `, [req.session.techId, req.session.operatorId]);

    if (!tech) {
      req.session.destroy();
      return res.redirect(`/${req.params.operatorAccount}/tech/login?error=Session expired`);
    }

    req.techContext = tech;
    next();
  } catch (error) {
    console.error('Tech auth middleware error:', error);
    res.redirect(`/${req.params.operatorAccount}/tech/login?error=Authentication failed`);
  }
};

// Tech dashboard - shows assigned issues
router.get('/:operatorAccount/tech/dashboard', requireTechAuth, async (req, res) => {
  try {
    const { formatLocalDate, formatLocalTime } = require('../utils/timezone');

    // Get operator's timezone offset
    const operator = await db.get(
      'SELECT timezone_offset FROM operators WHERE id = ?',
      [req.techContext.operator_id]
    );
    const timezoneOffset = operator?.timezone_offset || 0;

    // Get issues assigned to this tech
    const issues = await db.all(`
      SELECT i.*,
             gpm.name as machine_name,
             pm.location_notes
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE i.assigned_tech_id = ?
      ORDER BY i.created_at DESC
    `, [req.techContext.id]);

    // Add formatted dates to each issue
    const issuesWithFormattedDates = issues.map(issue => ({
      ...issue,
      formatted_created_date: formatLocalDate(issue.created_at, timezoneOffset),
      formatted_created_time: formatLocalTime(issue.created_at, timezoneOffset)
    }));

    res.render('tech/dashboard', {
      title: 'My Issues',
      tech: req.techContext,
      issues: issuesWithFormattedDates,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Tech dashboard error:', error);
    res.render('tech/dashboard', {
      title: 'My Issues',
      tech: req.techContext,
      issues: [],
      error: 'Failed to load issues'
    });
  }
});

// View individual issue (tech view)
router.get('/:operatorAccount/tech/issues/:id', requireTechAuth, async (req, res) => {
  try {
    const { formatLocalDate, formatLocalTime } = require('../utils/timezone');

    // Get operator's timezone offset
    const operator = await db.get(
      'SELECT timezone_offset FROM operators WHERE id = ?',
      [req.techContext.operator_id]
    );
    const timezoneOffset = operator?.timezone_offset || 0;

    const issueId = req.params.id;

    // Get issue details - only if assigned to this tech
    const issue = await db.get(`
      SELECT i.*,
             gpm.name as machine_name,
             gpm.manufacturer,
             gpm.machine_type,
             pm.location_notes,
             pm.status as machine_status,
             o.name as operator_name,
             comment_tech.name as comment_tech_name,
             comment_operator.name as comment_operator_name
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      JOIN operators o ON i.operator_id = o.id
      LEFT JOIN techs comment_tech ON i.resolved_by_tech_id = comment_tech.id
      LEFT JOIN operators comment_operator ON i.resolved_by_operator_id = comment_operator.id
      WHERE i.id = ? AND i.assigned_tech_id = ?
    `, [issueId, req.techContext.id]);

    if (!issue) {
      return res.redirect(`/${req.techContext.operator_username}/tech/dashboard?error=Issue not found or not assigned to you`);
    }

    // Get recently not duplicated issues for the same machine
    const notDuplicatedIssues = await db.all(`
      SELECT i.id, i.issue_type, i.created_at, i.assigned_tech_id, i.assigned_at,
             t.name as tech_name
      FROM issues i
      LEFT JOIN techs t ON i.assigned_tech_id = t.id
      WHERE i.machine_id = ?
        AND i.operator_id = ?
        AND i.status = 'Not Duplicated'
        AND i.id != ?
      ORDER BY i.created_at DESC
      LIMIT 10
    `, [issue.machine_id, req.techContext.operator_id, issueId]);

    // Add formatted dates to issue
    issue.formatted_created_date = formatLocalDate(issue.created_at, timezoneOffset);
    issue.formatted_created_time = formatLocalTime(issue.created_at, timezoneOffset);

    if (issue.assigned_at) {
      issue.formatted_assigned_date = formatLocalDate(issue.assigned_at, timezoneOffset);
      issue.formatted_assigned_time = formatLocalTime(issue.assigned_at, timezoneOffset);
    }

    if (issue.resolved_at) {
      issue.formatted_resolved_date = formatLocalDate(issue.resolved_at, timezoneOffset);
      issue.formatted_resolved_time = formatLocalTime(issue.resolved_at, timezoneOffset);
    }

    // Add formatted dates to not duplicated issues
    const notDuplicatedIssuesWithDates = notDuplicatedIssues.map(notDupIssue => ({
      ...notDupIssue,
      formatted_created_date: formatLocalDate(notDupIssue.created_at, timezoneOffset)
    }));

    res.render('tech/issue-details', {
      title: `Issue #${issue.id} - ${issue.issue_type}`,
      tech: req.techContext,
      issue,
      notDuplicatedIssues: notDuplicatedIssuesWithDates,
      success: req.query.success,
      error: req.query.error
    });
  } catch (error) {
    console.error('Tech issue details error:', error);
    res.redirect(`/${req.techContext.operator_username}/tech/dashboard?error=Failed to load issue details`);
  }
});

// Tech resolve issue
router.post('/:operatorAccount/tech/issues/:id/resolve', requireTechAuth, async (req, res) => {
  try {
    const issueId = req.params.id;
    const { tech_comments } = req.body;

    // Verify issue is assigned to this tech and get issue details
    const issue = await db.get(`
      SELECT i.id, i.issue_type, i.operator_id, gpm.name as machine_name
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE i.id = ? AND i.assigned_tech_id = ?
    `, [issueId, req.techContext.id]);

    if (!issue) {
      return res.redirect(`/${req.techContext.operator_username}/tech/dashboard?error=Issue not found or not assigned to you`);
    }

    // Update issue status to resolved
    await db.run(`
      UPDATE issues
      SET status = 'Resolved',
          resolved_at = datetime('now'),
          tech_comment = ?,
          resolved_by_tech_id = ?
      WHERE id = ?
    `, [tech_comments || null, req.techContext.id, issueId]);

    // Log activity
    await logActivity('issue_resolved', {
      operatorId: issue.operator_id,
      issueId: issueId,
      techId: req.techContext.id,
      actorType: 'tech',
      actorName: req.techContext.name,
      description: `Issue "${issue.issue_type}" resolved by technician for ${issue.machine_name}`
    });

    res.redirect(`/${req.techContext.operator_username}/tech/issues/${issueId}?success=Issue marked as resolved`);
  } catch (error) {
    console.error('Tech resolve issue error:', error);
    res.redirect(`/${req.techContext.operator_username}/tech/issues/${req.params.id}?error=Failed to resolve issue`);
  }
});

// Tech mark issue as not duplicated
router.post('/:operatorAccount/tech/issues/:id/not-duplicated', requireTechAuth, async (req, res) => {
  try {
    const issueId = req.params.id;
    const { tech_comments } = req.body;

    // Verify issue is assigned to this tech and get issue details
    const issue = await db.get(`
      SELECT i.id, i.issue_type, i.operator_id, gpm.name as machine_name
      FROM issues i
      JOIN pinball_machines pm ON i.machine_id = pm.id
      JOIN global_pinball_machines gpm ON pm.global_machine_id = gpm.id
      WHERE i.id = ? AND i.assigned_tech_id = ?
    `, [issueId, req.techContext.id]);

    if (!issue) {
      return res.redirect(`/${req.techContext.operator_username}/tech/dashboard?error=Issue not found or not assigned to you`);
    }

    // Update issue status to not duplicated
    await db.run(`
      UPDATE issues
      SET status = 'Not Duplicated',
          tech_comment = ?,
          resolved_by_tech_id = ?
      WHERE id = ?
    `, [tech_comments || null, req.techContext.id, issueId]);

    // Log activity
    await logActivity('issue_not_duplicated', {
      operatorId: issue.operator_id,
      issueId: issueId,
      techId: req.techContext.id,
      actorType: 'tech',
      actorName: req.techContext.name,
      description: `Issue "${issue.issue_type}" marked as not duplicated by technician for ${issue.machine_name}`
    });

    res.redirect(`/${req.techContext.operator_username}/tech/issues/${issueId}?success=Issue marked as not duplicated`);
  } catch (error) {
    console.error('Tech mark not duplicated error:', error);
    res.redirect(`/${req.techContext.operator_username}/tech/issues/${req.params.id}?error=Failed to mark issue as not duplicated`);
  }
});

module.exports = router;
