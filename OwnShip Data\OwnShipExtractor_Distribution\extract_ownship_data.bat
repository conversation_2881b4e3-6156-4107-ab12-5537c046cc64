@echo off
REM Own Ship Data Extractor - Batch file for Windows Task Scheduler
REM This script automatically extracts the last 24 hours of data from OwnShip Recorder

REM Set the path to this script's directory
cd /d "%~dp0"

REM Default parameters - modify as needed
set HOURS=24
set OUTPUT_FOLDER=%USERPROFILE%\Documents\OwnShipData
set USE_LOCAL_TIME=false

REM Create output folder if it doesn't exist
if not exist "%OUTPUT_FOLDER%" mkdir "%OUTPUT_FOLDER%"

REM Run the extraction
echo Starting Own Ship Data extraction...
echo Time: %date% %time%
echo Output folder: %OUTPUT_FOLDER%
echo Time frame: %HOURS% hours

python cli_interface.py --detect
if errorlevel 1 (
    echo Error: Could not detect databases
    exit /b 1
)

REM Try to find a valid database automatically
python cli_interface.py --output "%OUTPUT_FOLDER%" --hours %HOURS%
if errorlevel 1 (
    echo Error: Extraction failed
    exit /b 1
)

echo Extraction completed successfully
echo Output saved to: %OUTPUT_FOLDER%
