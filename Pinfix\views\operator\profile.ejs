<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/<%= operatorContext.username %>/admin">
                <i class="bi bi-tools"></i> PinFix - <%= operatorContext.name %>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operatorContext.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operatorContext.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operatorContext.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operatorContext.username %>/admin/techs">Manage Tech</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <%= operatorContext.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="/<%= operatorContext.username %>/admin/profile">
                                <i class="bi bi-person-gear"></i> Profile Settings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/auth/logout">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1><i class="bi bi-person-gear"></i> Profile Settings</h1>
                <p class="text-muted">Manage your account information and notification preferences</p>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <% if (typeof success !== 'undefined' && success) { %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-person"></i> Account Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/<%= operatorContext.username %>/admin/profile">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Business Name *</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="name" 
                                           name="name" 
                                           value="<%= operator.name %>"
                                           required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           value="<%= operator.email %>"
                                           required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" 
                                           class="form-control" 
                                           id="phone" 
                                           name="phone" 
                                           value="<%= operator.phone || '' %>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="username" 
                                           value="<%= operator.username %>"
                                           disabled>
                                    <div class="form-text">Username cannot be changed</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Business Address</label>
                                <textarea class="form-control"
                                          id="address"
                                          name="address"
                                          rows="3"><%= operator.address || '' %></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="timezone_offset" class="form-label">Timezone</label>
                                <select class="form-select" id="timezone_offset" name="timezone_offset">
                                    <% timezoneOptions.forEach(function(timezone) { %>
                                        <option value="<%= timezone.value %>"
                                                <%= (operator.timezone_offset === timezone.value) ? 'selected' : '' %>>
                                            <%= timezone.label %>
                                        </option>
                                    <% }); %>
                                </select>
                                <div class="form-text">Used for displaying dates and times in your local timezone</div>
                            </div>

                            <!-- Password Change Section -->
                            <hr class="my-4">
                            <div class="mb-4">
                                <h6 class="text-primary">
                                    <i class="bi bi-key"></i> Change Password
                                </h6>
                                <p class="text-muted small">Leave blank to keep your current password.</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password"
                                           class="form-control"
                                           id="password"
                                           name="password"
                                           minlength="6">
                                    <div class="form-text">Minimum 6 characters</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                                    <input type="password"
                                           class="form-control"
                                           id="confirm_password"
                                           name="confirm_password"
                                           minlength="6">
                                    <div class="form-text">Must match new password</div>
                                </div>
                            </div>

                            <!-- Email Notifications Section -->
                            <% if (operator.email_notifications_enabled) { %>
                                <hr class="my-4">
                                <div class="mb-4">
                                    <h6 class="text-primary">
                                        <i class="bi bi-envelope"></i> Email Notification Preferences
                                    </h6>
                                    <p class="text-muted small">Control when you receive email notifications from PinFix.</p>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="operator_notifications_enabled" 
                                                   name="operator_notifications_enabled"
                                                   <%= operator.operator_notifications_enabled ? 'checked' : '' %>>
                                            <label class="form-check-label" for="operator_notifications_enabled">
                                                <strong>Issue Notifications</strong>
                                            </label>
                                            <div class="form-text">
                                                <i class="bi bi-info-circle"></i> 
                                                Receive emails when customers report new issues
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="tech_notifications_enabled" 
                                                   name="tech_notifications_enabled"
                                                   <%= operator.tech_notifications_enabled ? 'checked' : '' %>>
                                            <label class="form-check-label" for="tech_notifications_enabled">
                                                <strong>Tech Notifications</strong>
                                            </label>
                                            <div class="form-text">
                                                <i class="bi bi-info-circle"></i> 
                                                Send emails to technicians when issues are assigned to them
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <% } else { %>
                                <hr class="my-4">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Email Notifications Unavailable</strong><br>
                                    Email notification features are not enabled for your account. 
                                    Contact support to enable email notifications.
                                </div>
                            <% } %>

                            <div class="d-flex justify-content-between">
                                <a href="/<%= operatorContext.username %>/admin" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg"></i> Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-shield-check"></i> Account Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-check-circle-fill text-success fs-4 me-2"></i>
                            <div>
                                <strong>Account Active</strong><br>
                                <small class="text-muted">Your account is in good standing</small>
                            </div>
                        </div>

                        <% if (operator.email_notifications_enabled) { %>
                            <div class="d-flex align-items-center mb-3">
                                <i class="bi bi-envelope-check-fill text-primary fs-4 me-2"></i>
                                <div>
                                    <strong>Email Notifications</strong><br>
                                    <small class="text-muted">Enabled for your account</small>
                                </div>
                            </div>
                        <% } else { %>
                            <div class="d-flex align-items-center mb-3">
                                <i class="bi bi-envelope-x-fill text-muted fs-4 me-2"></i>
                                <div>
                                    <strong>Email Notifications</strong><br>
                                    <small class="text-muted">Not available</small>
                                </div>
                            </div>
                        <% } %>

                        <div class="d-flex align-items-center">
                            <i class="bi bi-controller text-info fs-4 me-2"></i>
                            <div>
                                <strong>Machine Limit</strong><br>
                                <small class="text-muted">
                                    <% if (typeof machineStats !== 'undefined') { %>
                                        <%= machineStats.limit %> machines allowed
                                    <% } else { %>
                                        <%= operator.machine_limit || 10 %> machines allowed
                                    <% } %>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-question-circle"></i> Need Help?</h6>
                    </div>
                    <div class="card-body">
                        <p class="small">If you need to change your username or have other account issues, please contact support.</p>
                        <a href="mailto:<%= process.env.SUPPORT_EMAIL || '<EMAIL>' %>?subject=Support%20for%20PinFix" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-envelope"></i> Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Password Validation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');
            const form = document.querySelector('form');

            function validatePasswords() {
                const password = passwordField.value;
                const confirmPassword = confirmPasswordField.value;

                if (password && confirmPassword) {
                    if (password !== confirmPassword) {
                        confirmPasswordField.setCustomValidity('Passwords do not match');
                        confirmPasswordField.classList.add('is-invalid');
                    } else {
                        confirmPasswordField.setCustomValidity('');
                        confirmPasswordField.classList.remove('is-invalid');
                        confirmPasswordField.classList.add('is-valid');
                    }
                } else {
                    confirmPasswordField.setCustomValidity('');
                    confirmPasswordField.classList.remove('is-invalid', 'is-valid');
                }
            }

            // Validate on input
            passwordField.addEventListener('input', validatePasswords);
            confirmPasswordField.addEventListener('input', validatePasswords);

            // Validate on form submit
            form.addEventListener('submit', function(e) {
                const password = passwordField.value;
                const confirmPassword = confirmPasswordField.value;

                if (password && password.length < 6) {
                    e.preventDefault();
                    passwordField.setCustomValidity('Password must be at least 6 characters long');
                    passwordField.classList.add('is-invalid');
                    return false;
                }

                if (password && password !== confirmPassword) {
                    e.preventDefault();
                    confirmPasswordField.setCustomValidity('Passwords do not match');
                    confirmPasswordField.classList.add('is-invalid');
                    return false;
                }
            });
        });
    </script>
</body>
</html>
