"""
Database Manager Module for Own Ship Extractor
Handles detection, validation, and selection of OwnShipRecorder.tzdb files
"""

import os
import sqlite3
from pathlib import Path
from typing import List, Optional, Tuple


class DatabaseManager:
    """Manages OwnShipRecorder database detection and validation"""
    
    # Standard TimeZero paths
    STANDARD_PATHS = [
        r"C:\ProgramData\TimeZero\DATA",
        r"C:\ProgramData\TimeZeroREC\DATA", 
        r"C:\ProgramData\TimeZeroVTS\DATA"
    ]
    
    # Product names for display
    PRODUCT_NAMES = {
        "TimeZero": "TZ PRO",
        "TimeZeroREC": "TZ NAV",
        "TimeZeroVTS": "TZ CM"
    }
    
    def __init__(self):
        self.detected_databases = []
        self.rootpath_redirections = {}
        self._load_rootpath_redirections()
    
    def _load_rootpath_redirections(self):
        """Load rootpath.txt redirections if they exist"""
        for path in self.STANDARD_PATHS:
            rootpath_file = os.path.join(path, "rootpath.txt")
            if os.path.exists(rootpath_file):
                try:
                    with open(rootpath_file, 'r', encoding='utf-8') as f:
                        redirect_path = f.read().strip()
                        if os.path.exists(redirect_path):
                            self.rootpath_redirections[path] = redirect_path
                except Exception as e:
                    print(f"Warning: Could not read rootpath.txt from {path}: {e}")
    
    def get_effective_path(self, standard_path: str) -> str:
        """Get the effective path considering rootpath.txt redirections"""
        return self.rootpath_redirections.get(standard_path, standard_path)
    
    def detect_databases(self) -> List[Tuple[str, str]]:
        """
        Detect OwnShipRecorder.tzdb files in standard locations
        Returns list of tuples: (display_name, file_path)
        """
        self.detected_databases = []
        
        for standard_path in self.STANDARD_PATHS:
            effective_path = self.get_effective_path(standard_path)
            db_path = os.path.join(effective_path, "OwnShipRecorder.tzdb")
            
            if os.path.exists(db_path):
                # Determine product name from path
                product_key = None
                if "TimeZeroREC" in standard_path:
                    product_key = "TimeZeroREC"
                elif "TimeZeroVTS" in standard_path:
                    product_key = "TimeZeroVTS"
                elif "TimeZero" in standard_path:
                    product_key = "TimeZero"

                display_name = self.PRODUCT_NAMES.get(product_key, "Unknown")
                self.detected_databases.append((display_name, db_path))
        
        return self.detected_databases
    
    def validate_database(self, db_path: str) -> Tuple[bool, str]:
        """
        Validate that the file is a valid OwnShipRecorder database
        Returns (is_valid, error_message)
        """
        if not os.path.exists(db_path):
            return False, "File does not exist"
        
        if not db_path.lower().endswith('.tzdb'):
            return False, "File is not a .tzdb file"
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check for required tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['Data', 'DataAvailability', 'ArchiverMetadata']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                conn.close()
                return False, f"Missing required tables: {', '.join(missing_tables)}"
            
            # Check Data table schema
            cursor.execute("PRAGMA table_info(Data)")
            columns = [row[1] for row in cursor.fetchall()]
            
            required_columns = ['Date', 'X', 'Y', 'Depth', 'CourseOverGround', 'SpeedOverGround']
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                conn.close()
                return False, f"Missing required columns in Data table: {', '.join(missing_columns)}"
            
            # Check if there's any data
            cursor.execute("SELECT COUNT(*) FROM Data")
            row_count = cursor.fetchone()[0]
            
            if row_count == 0:
                conn.close()
                return False, "Database contains no data"
            
            conn.close()
            return True, "Valid OwnShipRecorder database"
            
        except sqlite3.Error as e:
            return False, f"Database error: {str(e)}"
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"
    
    def get_date_range(self, db_path: str) -> Tuple[Optional[int], Optional[int]]:
        """
        Get the date range of data in the database
        Returns (min_date_ms, max_date_ms) in TimeZero format
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT MIN(Date), MAX(Date) FROM Data")
            min_date, max_date = cursor.fetchone()
            
            conn.close()
            return min_date, max_date
            
        except Exception as e:
            print(f"Error getting date range: {e}")
            return None, None
    
    def get_database_info(self, db_path: str) -> dict:
        """Get comprehensive information about the database"""
        info = {
            'path': db_path,
            'valid': False,
            'error': None,
            'date_range': (None, None),
            'record_count': 0
        }
        
        is_valid, error_msg = self.validate_database(db_path)
        info['valid'] = is_valid
        info['error'] = error_msg
        
        if is_valid:
            info['date_range'] = self.get_date_range(db_path)
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM Data")
                info['record_count'] = cursor.fetchone()[0]
                conn.close()
            except Exception as e:
                info['error'] = f"Could not get record count: {e}"
        
        return info


if __name__ == "__main__":
    # Test the database manager
    db_manager = DatabaseManager()
    
    print("Detecting databases...")
    detected = db_manager.detect_databases()
    
    if detected:
        print(f"Found {len(detected)} database(s):")
        for display_name, path in detected:
            print(f"  {display_name}: {path}")
            
            info = db_manager.get_database_info(path)
            print(f"    Valid: {info['valid']}")
            if info['error']:
                print(f"    Error: {info['error']}")
            if info['valid']:
                print(f"    Records: {info['record_count']}")
                print(f"    Date range: {info['date_range']}")
    else:
        print("No databases found in standard locations")
    
    # Test with current directory database
    current_db = "OwnShipRecorder.tzdb"
    if os.path.exists(current_db):
        print(f"\nTesting current directory database: {current_db}")
        info = db_manager.get_database_info(current_db)
        print(f"  Valid: {info['valid']}")
        if info['error']:
            print(f"  Error: {info['error']}")
        if info['valid']:
            print(f"  Records: {info['record_count']}")
            print(f"  Date range: {info['date_range']}")
