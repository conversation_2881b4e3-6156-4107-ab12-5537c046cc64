import sqlite3
import sys

def examine_database(db_path):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"Tables: {tables}")
        
        # Examine each table
        for table in tables:
            print(f"\n--- Table: {table} ---")
            cursor.execute(f"PRAGMA table_info({table})")
            schema = cursor.fetchall()
            print("Schema:")
            for col in schema:
                print(f"  {col}")
            
            # Get sample data
            cursor.execute(f"SELECT * FROM {table} LIMIT 3")
            sample_data = cursor.fetchall()
            print("Sample data:")
            for row in sample_data:
                print(f"  {row}")
        
        # Check for views
        cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
        views = [row[0] for row in cursor.fetchall()]
        print(f"\nViews: {views}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    examine_database("OwnShipRecorder.tzdb")
