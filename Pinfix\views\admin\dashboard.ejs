<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>


    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-tools"></i> PinFix - Super Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/operators">Operators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/machines">Global Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/reports">Reports</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> Super Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-shield-lock"></i> Super Admin Dashboard</h1>
                    <div>
                        <span class="badge bg-success">Logged in as: <%= session.superAdmin.username %></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Total Operators</h6>
                                <div class="stats-number"><%= stats.operators %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-buildings fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Active Operators</h6>
                                <div class="stats-number"><%= stats.activeOperators %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-check-circle fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Total Issues</h6>
                                <div class="stats-number"><%= stats.issues %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-exclamation-triangle fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-white-50">Pinball Machines</h6>
                                <div class="stats-number"><%= stats.machines %></div>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-controller fs-1 text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <a href="/admin/operators" class="btn btn-primary w-100">
                                    <i class="bi bi-buildings"></i> Manage Operators
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="/admin/machines" class="btn btn-success w-100">
                                    <i class="bi bi-controller"></i> Global Machines
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="/admin/reports" class="btn btn-info w-100">
                                    <i class="bi bi-graph-up"></i> System Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <% if (activities && activities.length > 0) { %>
                            <div class="activity-list">
                                <% activities.forEach((activity, index) => { %>
                                    <div class="activity-item d-flex align-items-start <%= index < activities.length - 1 ? 'border-bottom pb-3 mb-3' : '' %>">
                                        <div class="activity-icon me-3">
                                            <i class="<%= activity.icon %> <%= activity.color %> fs-5"></i>
                                        </div>
                                        <div class="activity-content flex-grow-1">
                                            <div class="activity-description">
                                                <%= activity.description %>
                                            </div>
                                            <div class="activity-meta">
                                                <small class="text-muted">
                                                    <% if (activity.operator_name) { %>
                                                        <span class="badge bg-light text-dark me-1">
                                                            <i class="bi bi-buildings"></i> <%= activity.operator_name %>
                                                        </span>
                                                    <% } %>
                                                    <% if (activity.machine_name) { %>
                                                        <span class="badge bg-light text-dark me-1">
                                                            <i class="bi bi-controller"></i> <%= activity.machine_name %>
                                                        </span>
                                                    <% } %>
                                                    <% if (activity.tech_name) { %>
                                                        <span class="badge bg-light text-dark me-1">
                                                            <i class="bi bi-person"></i> <%= activity.tech_name %>
                                                        </span>
                                                    <% } %>
                                                </small>
                                            </div>
                                        </div>
                                        <div class="activity-time">
                                            <small class="text-muted"><%= activity.formatted_time %></small>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        <% } else { %>
                            <div class="text-center py-4">
                                <i class="bi bi-clock-history text-muted fs-1"></i>
                                <p class="text-muted mt-2">No recent activity to display.</p>
                                <small class="text-muted">Activity will appear here as operators and techs use the system.</small>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
</body>
</html>
