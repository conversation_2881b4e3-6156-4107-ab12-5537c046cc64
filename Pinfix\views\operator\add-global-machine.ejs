<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/machines">Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/issues">Issues</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/techs">Techs</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/<%= operator.username %>/admin/profile">Profile</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <% if (operator.logo_path) { %>
                                <img src="<%= operator.logo_path %>" alt="Logo" style="height: 24px; width: 24px; border-radius: 50%; margin-right: 8px;">
                            <% } else { %>
                                <i class="bi bi-buildings"></i>
                            <% } %>
                            <%= operator.name %>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/<%= operator.username %>/admin/profile">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="/auth/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-plus-circle"></i> Add Machine to Global Database
                    </h1>
                    <a href="/<%= operator.username %>/admin/machines/new" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Add Machine
                    </a>
                </div>
            </div>
        </div>

        <!-- Info Alert -->
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle"></i>
                    <strong>Help us grow the database!</strong> 
                    Add your machine to our global database so other operators can benefit from it too. 
                    Once added, you'll be redirected back to add it to your inventory.
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Machine Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Machine Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/<%= operator.username %>/admin/machines/add-global">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Machine Name *</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="name" 
                                           name="name" 
                                           value="<%= machine ? machine.name : '' %>" 
                                           required>
                                    <div class="form-text">The official name of the pinball machine</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="manufacturer" class="form-label">Manufacturer *</label>
                                    <select class="form-select" id="manufacturer" name="manufacturer" required>
                                        <option value="">Select Manufacturer</option>
                                        <option value="Stern" <%= machine && machine.manufacturer === 'Stern' ? 'selected' : '' %>>Stern</option>
                                        <option value="Jersey Jack Pinball" <%= machine && machine.manufacturer === 'Jersey Jack Pinball' ? 'selected' : '' %>>Jersey Jack Pinball</option>
                                        <option value="Spooky Pinball" <%= machine && machine.manufacturer === 'Spooky Pinball' ? 'selected' : '' %>>Spooky Pinball</option>
                                        <option value="Chicago Gaming Company" <%= machine && machine.manufacturer === 'Chicago Gaming Company' ? 'selected' : '' %>>Chicago Gaming Company</option>
                                        <option value="Multimorphic" <%= machine && machine.manufacturer === 'Multimorphic' ? 'selected' : '' %>>Multimorphic</option>
                                        <option value="Pinball Brothers" <%= machine && machine.manufacturer === 'Pinball Brothers' ? 'selected' : '' %>>Pinball Brothers</option>
                                        <option value="Bally" <%= machine && machine.manufacturer === 'Bally' ? 'selected' : '' %>>Bally</option>
                                        <option value="Williams" <%= machine && machine.manufacturer === 'Williams' ? 'selected' : '' %>>Williams</option>
                                        <option value="Gottlieb" <%= machine && machine.manufacturer === 'Gottlieb' ? 'selected' : '' %>>Gottlieb</option>
                                        <option value="Other" <%= machine && machine.manufacturer === 'Other' ? 'selected' : '' %>>Other</option>
                                    </select>
                                    <div class="form-text">The company that manufactured this machine</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="date_of_manufacture" class="form-label">Year of Manufacture</label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="date_of_manufacture" 
                                           name="date_of_manufacture" 
                                           value="<%= machine ? (machine.date_of_manufacture || '') : '' %>"
                                           min="1930" 
                                           max="2030">
                                    <div class="form-text">The year this machine was manufactured</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="machine_type" class="form-label">Machine Type *</label>
                                    <select class="form-select" id="machine_type" name="machine_type" required>
                                        <option value="">Select Type</option>
                                        <option value="EM" <%= machine && machine.machine_type === 'EM' ? 'selected' : '' %>>EM (Electromechanical)</option>
                                        <option value="SS Alpha" <%= machine && machine.machine_type === 'SS Alpha' ? 'selected' : '' %>>SS Alpha (Solid State Alphanumeric)</option>
                                        <option value="SS DMD" <%= machine && machine.machine_type === 'SS DMD' ? 'selected' : '' %>>SS DMD (Solid State Dot Matrix)</option>
                                        <option value="SS LCD" <%= machine && machine.machine_type === 'SS LCD' ? 'selected' : '' %>>SS LCD (Solid State LCD)</option>
                                    </select>
                                    <div class="form-text">The technology type of this machine</div>
                                </div>
                            </div>

                            <!-- Machine Type Descriptions -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Machine Type Guide</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>EM:</strong> Electromechanical (1930s-1970s)</p>
                                                    <p class="mb-1"><strong>SS Alpha:</strong> Alphanumeric displays (1970s-1980s)</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>SS DMD:</strong> Dot Matrix Display (1990s-2000s)</p>
                                                    <p class="mb-1"><strong>SS LCD:</strong> LCD Screen (2010s-present)</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="/<%= operator.username %>/admin/machines/new" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-plus-circle"></i> Add to Global Database
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Common JS -->
    <script src="/static/js/common.js"></script>
    <!-- Add Global Machine JS -->
    <script src="/static/js/add-global-machine.js"></script>
</body>
</html>
