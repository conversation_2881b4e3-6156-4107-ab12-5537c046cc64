apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: call-analysis-data
  labels:
    app: call-analysis
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  # Uncomment and modify if you need a specific storage class
  # storageClassName: "your-storage-class"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: call-analysis-reports
  labels:
    app: call-analysis
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
  # Uncomment and modify if you need a specific storage class
  # storageClassName: "your-storage-class"
