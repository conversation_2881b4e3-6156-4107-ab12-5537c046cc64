# Own Ship Extractor - Standalone Distribution

## Files Included:
- OwnShipExtractor.exe - Main application (GUI and CLI)
- README.md - Complete documentation
- BUILD_GUIDE.md - Instructions for building from source
- extract_ownship_data.bat - Batch file for Windows Task Scheduler
- USAGE.txt - This quick start guide

## Quick Start:

### GUI Mode:
Double-click OwnShipExtractor.exe to open the graphical interface.

### Command Line Mode:
Open Command Prompt in this folder and run:
OwnShipExtractor.exe "path\to\OwnShipRecorder.tzdb" --output "output_folder" --hours 24

### Examples:
OwnShipExtractor.exe --detect
OwnShipExtractor.exe "C:\ProgramData\TimeZero\DATA\OwnShipRecorder.tzdb" --hours 24
OwnShipExtractor.exe "database.tzdb" --output "C:\Exports" --hours 48 --local

### Automated Mode (Windows Task Scheduler):
Use extract_ownship_data.bat with Windows Task Scheduler for automatic data extraction.

## System Requirements:
- Windows 7 or later (64-bit recommended)
- No Python installation required
- ~100 MB free disk space
- TimeZero software with OwnShip Recorder data

## Features:
- Automatic database detection (TZ PRO, TZ NAV, TZ CM)
- Proper coordinate conversion (Mercator projection)
- Accurate date/time conversion
- Data visualization with dual-line graphs
- CSV export with all navigation data
- Progress bar for large datasets
- UTC and Local time support

## Troubleshooting:
- If the executable doesn't start, try running from Command Prompt to see error messages
- Antivirus software may flag the executable as suspicious (false positive)
- For detailed documentation, see README.md

For complete documentation and advanced usage, see README.md
