// Machines page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle URL parameters for success/error messages
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.get('success')) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${urlParams.get('success')}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        const mainContainer = document.querySelector('main .container-fluid');
        if (mainContainer) {
            mainContainer.insertBefore(alertDiv, mainContainer.firstChild);
        }
    }
    
    if (urlParams.get('error')) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${urlParams.get('error')}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        const mainContainer = document.querySelector('main .container-fluid');
        if (mainContainer) {
            mainContainer.insertBefore(alertDiv, mainContainer.firstChild);
        }
    }
});
