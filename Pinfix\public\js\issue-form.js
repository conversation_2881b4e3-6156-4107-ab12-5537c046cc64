// Issue form functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss error/success alerts after 5 seconds (but not info alerts)
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert-danger, .alert-success, .alert-warning');
        alerts.forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const issueType = document.getElementById('issue_type').value;
            const priority = document.getElementById('priority').value;

            if (!issueType || !priority) {
                e.preventDefault();
                alert('Please fill in all required fields (Issue Type and Priority)');
                return false;
            }
        });
    }
});
