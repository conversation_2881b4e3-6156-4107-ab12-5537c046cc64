<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        .assigned-row {
            background-color: #fff3cd !important;
            border-left: 4px solid #ffc107 !important;
            box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2) !important;
        }
        .assigned-row:hover {
            background-color: #ffeaa7 !important;
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3) !important;
        }
        .assigned-row td {
            border-color: #ffc107 !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-tools"></i> PinFix
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-gear"></i> <%= tech.name %>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <form method="POST" action="/<%= tech.operator_username %>/tech/logout" class="d-inline">
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right"></i> Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Success/Error Messages -->
        <% if (success) { %>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
        <% if (error) { %>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>

        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <h1><i class="bi bi-person-gear"></i> My Issues</h1>
                <p class="text-muted">Issues assigned to you at <%= tech.operator_name %></p>
            </div>
        </div>

        <!-- Filter Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center filter-card" data-status="all" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-info">All Issues</h5>
                        <h2 class="text-info"><%= issues.length %></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center filter-card" data-status="Assigned" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-warning">Assigned</h5>
                        <h2 class="text-warning"><%= issues.filter(i => i.status === 'Assigned').length %></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center filter-card" data-status="Resolved" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-success">Resolved</h5>
                        <h2 class="text-success"><%= issues.filter(i => i.status === 'Resolved').length %></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center filter-card" data-status="Not Duplicated" style="cursor: pointer;">
                    <div class="card-body">
                        <h5 class="card-title text-secondary">Not Duplicated</h5>
                        <h2 class="text-secondary"><%= issues.filter(i => i.status === 'Not Duplicated').length %></h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Issues Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">My Assigned Issues</h5>
                        <div>
                            <span id="filter-status" class="badge bg-info me-2" style="display: none;"></span>
                            <button id="clear-filters" class="btn btn-sm btn-outline-secondary" style="display: none;">
                                <i class="bi bi-x-circle"></i> Clear Filters
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <% if (issues.length === 0) { %>
                            <div class="text-center py-5">
                                <i class="bi bi-clipboard-check display-1 text-muted"></i>
                                <h4 class="text-muted mt-3">No Issues Assigned</h4>
                                <p class="text-muted">You don't have any issues assigned to you at the moment.</p>
                            </div>
                        <% } else { %>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Machine</th>
                                            <th>Issue Type</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% issues.forEach(issue => { %>
                                            <tr class="issue-row <%= issue.status === 'Assigned' ? 'assigned-row' : '' %>" data-status="<%= issue.status %>">
                                                <td>
                                                    <strong>#<%= issue.id %></strong>
                                                </td>
                                                <td>
                                                    <div>
                                                        <%= issue.machine_name %>
                                                        <% if (issue.location_notes) { %>
                                                            <br><small class="text-muted">
                                                                <i class="bi bi-geo-alt"></i> <%= issue.location_notes %>
                                                            </small>
                                                        <% } %>
                                                    </div>
                                                </td>
                                                <td><%= issue.issue_type %></td>
                                                <td>
                                                    <% 
                                                    let priorityClass = 'bg-success'; // Default green for Low
                                                    if (issue.priority === 'Medium') priorityClass = 'bg-warning';
                                                    else if (issue.priority === 'High') priorityClass = 'bg-danger';
                                                    %>
                                                    <span class="badge <%= priorityClass %>"><%= issue.priority %></span>
                                                </td>
                                                <td>
                                                    <% 
                                                    let statusClass = 'bg-warning'; // Default yellow for everything else
                                                    if (issue.status === 'Open') statusClass = 'bg-danger';
                                                    else if (issue.status === 'Resolved') statusClass = 'bg-success';
                                                    %>
                                                    <span class="badge <%= statusClass %>"><%= issue.status %></span>
                                                </td>
                                                <td>
                                                    <%= issue.formatted_created_date %>
                                                    <br><small class="text-muted"><%= issue.formatted_created_time %></small>
                                                </td>
                                                <td>
                                                    <a href="/<%= tech.operator_username %>/tech/issues/<%= issue.id %>" 
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="bi bi-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
    <!-- Issues Filter JS -->
    <script src="/static/js/tech-filter.js"></script>
</body>
</html>
