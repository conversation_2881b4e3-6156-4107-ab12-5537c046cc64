<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> | PinFix</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/admin">
                <i class="bi bi-tools"></i> PinFix - Super Admin
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/operators">Operators</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/machines">Global Machines</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/reports">Reports</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> Super Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/logout">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>
                        <i class="bi bi-<%= isEdit ? 'pencil' : 'plus-circle' %>"></i> 
                        <%= isEdit ? 'Edit' : 'Create' %> Operator
                    </h1>
                    <a href="/admin/operators" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Operators
                    </a>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <% if (typeof error !== 'undefined') { %>
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            </div>
        <% } %>

        <!-- Operator Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Operator Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="<%= isEdit ? `/admin/operators/${operator.id}` : '/admin/operators' %>">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">Business Name *</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="name" 
                                           name="name" 
                                           value="<%= operator ? operator.name : '' %>" 
                                           required>
                                    <div class="form-text">The official business name</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           value="<%= operator ? operator.email : '' %>" 
                                           required>
                                    <div class="form-text">Primary contact email</div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" 
                                           class="form-control" 
                                           id="phone" 
                                           name="phone" 
                                           value="<%= operator ? (operator.phone || '') : '' %>">
                                    <div class="form-text">Contact phone number</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <%= (operator && operator.status === 'active') || !operator ? 'selected' : '' %>>Active</option>
                                        <option value="inactive" <%= operator && operator.status === 'inactive' ? 'selected' : '' %>>Inactive</option>
                                    </select>
                                    <div class="form-text">Account status</div>
                                </div>
                            </div>

                            <!-- Email Notifications Section -->
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <h6 class="text-primary">
                                        <i class="bi bi-envelope"></i> Email Notifications
                                    </h6>
                                    <p class="text-muted small">Control email notification features for this operator. Only enable for paying customers.</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="email_notifications_enabled"
                                               name="email_notifications_enabled"
                                               <%= operator && operator.email_notifications_enabled ? 'checked' : '' %>>
                                        <label class="form-check-label" for="email_notifications_enabled">
                                            <strong>Enable Email Notifications</strong>
                                        </label>
                                        <div class="form-text">
                                            <i class="bi bi-info-circle"></i>
                                            Master switch - allows this operator to use email notification features.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="machine_limit" class="form-label">Machine Limit</label>
                                    <input type="number"
                                           class="form-control"
                                           id="machine_limit"
                                           name="machine_limit"
                                           value="<%= operator ? (operator.machine_limit || 10) : 10 %>"
                                           min="1"
                                           max="1000"
                                           required>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle"></i>
                                        Maximum number of pinball machines this operator can add to their inventory.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control" 
                                          id="address" 
                                          name="address" 
                                          rows="3"><%= operator ? (operator.address || '') : '' %></textarea>
                                <div class="form-text">Business address</div>
                            </div>

                            <hr class="my-4">

                            <h6 class="mb-3">Login Credentials</h6>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="username" 
                                           name="username" 
                                           value="<%= operator ? operator.username : '' %>" 
                                           required>
                                    <div class="form-text">Login username (must be unique)</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        Password <%= isEdit ? '' : '*' %>
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           <%= isEdit ? '' : 'required' %>>
                                    <div class="form-text">
                                        <%= isEdit ? 'Leave blank to keep current password' : 'Minimum 6 characters' %>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="/admin/operators" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-<%= isEdit ? 'check-circle' : 'plus-circle' %>"></i> 
                                    <%= isEdit ? 'Update' : 'Create' %> Operator
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <% if (isEdit) { %>
                    <!-- Additional Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Debug Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <form method="POST" action="/admin/operators/<%= operator.id %>/regenerate-all-qr" class="d-inline">
                                        <button type="submit" class="btn btn-outline-warning w-100"
                                                onclick="return confirm('Regenerate QR codes for all machines of <%= operator.name %>? This will update all existing QR codes with the new format.')">
                                            <i class="bi bi-arrow-clockwise"></i> Regenerate All QR Codes
                                        </button>
                                    </form>
                                    <div class="form-text mt-2">
                                        Regenerates QR codes for all machines belonging to this operator. Use this for debugging or when QR code format changes.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Iker Pryszo. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>

    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const isEdit = <%= isEdit ? 'true' : 'false' %>;
            
            if (!isEdit && password.length < 6) {
                e.preventDefault();
                alert('Password must be at least 6 characters long');
                return false;
            }
            
            if (isEdit && password && password.length < 6) {
                e.preventDefault();
                alert('Password must be at least 6 characters long');
                return false;
            }
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
